#!/bin/bash

# Environment Diagnostic Script
# Usage: ./scripts/diagnose-env.sh

# Source utility functions
source "$(dirname "$0")/utils.sh"

print_status "Docker Local Development Environment Diagnostics"
echo "=================================================="

# Check Docker
print_status "Checking Docker..."
if command -v docker >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓ Docker is installed${NC}"
    docker --version
    
    if docker info >/dev/null 2>&1; then
        echo -e "  ${GREEN}✓ Docker is running${NC}"
        
        # Check Docker resources
        echo "  Docker system info:"
        docker system df
    else
        echo -e "  ${RED}✗ Docker is not running${NC}"
    fi
else
    echo -e "  ${RED}✗ Docker is not installed${NC}"
fi

echo ""

# Check Docker Compose
print_status "Checking Docker Compose..."
if command -v docker-compose >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓ Docker Compose is installed${NC}"
    docker-compose --version
else
    echo -e "  ${RED}✗ Docker Compose is not installed${NC}"
fi

echo ""

# Check ports
print_status "Checking port availability..."
ports=(3000 5432 4566 6379 5050 9229)
for port in "${ports[@]}"; do
    if lsof -i :$port >/dev/null 2>&1; then
        process=$(lsof -i :$port | tail -n 1 | awk '{print $1}')
        echo -e "  ${YELLOW}⚠ Port $port is in use by $process${NC}"
    else
        echo -e "  ${GREEN}✓ Port $port is available${NC}"
    fi
done

echo ""

# Check environment files
print_status "Checking environment files..."
if [ -f ".env" ]; then
    echo -e "  ${GREEN}✓ .env file exists${NC}"
else
    echo -e "  ${YELLOW}⚠ .env file not found${NC}"
fi

if [ -f ".env.local" ]; then
    echo -e "  ${GREEN}✓ .env.local file exists${NC}"
else
    echo -e "  ${RED}✗ .env.local file not found${NC}"
fi

echo ""

# Check Docker services
print_status "Checking Docker services..."
if docker-compose ps >/dev/null 2>&1; then
    echo "  Service status:"
    docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
else
    echo -e "  ${YELLOW}⚠ No Docker services running${NC}"
fi

echo ""

# Check volumes
print_status "Checking Docker volumes..."
volumes=$(docker volume ls --filter name=ivent_api --format "{{.Name}}")
if [ -n "$volumes" ]; then
    echo "  Existing volumes:"
    echo "$volumes" | sed 's/^/    /'
else
    echo -e "  ${YELLOW}⚠ No project volumes found${NC}"
fi

echo ""

# Check networks
print_status "Checking Docker networks..."
networks=$(docker network ls --filter name=ivent --format "{{.Name}}")
if [ -n "$networks" ]; then
    echo "  Existing networks:"
    echo "$networks" | sed 's/^/    /'
else
    echo -e "  ${YELLOW}⚠ No project networks found${NC}"
fi

echo ""

# Check AWS CLI (for LocalStack)
print_status "Checking AWS CLI..."
if command -v aws >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓ AWS CLI is installed${NC}"
    aws --version
else
    echo -e "  ${YELLOW}⚠ AWS CLI is not installed (needed for LocalStack S3 testing)${NC}"
    echo "    Install with: pip install awscli"
fi

echo ""

# Check curl
print_status "Checking curl..."
if command -v curl >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓ curl is installed${NC}"
else
    echo -e "  ${RED}✗ curl is not installed${NC}"
fi

echo ""

# Check Node.js
print_status "Checking Node.js..."
if command -v node >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓ Node.js is installed${NC}"
    node --version
    
    if command -v npm >/dev/null 2>&1; then
        echo -e "  ${GREEN}✓ npm is installed${NC}"
        npm --version
    else
        echo -e "  ${RED}✗ npm is not installed${NC}"
    fi
else
    echo -e "  ${YELLOW}⚠ Node.js is not installed (optional for Docker development)${NC}"
fi

echo ""

# Test connectivity
print_status "Testing service connectivity..."

# Test PostgreSQL
if docker-compose ps postgres | grep -q "Up"; then
    if docker-compose exec -T postgres pg_isready -U ivent_user -d ivent_dev >/dev/null 2>&1; then
        echo -e "  ${GREEN}✓ PostgreSQL is accessible${NC}"
    else
        echo -e "  ${RED}✗ PostgreSQL is not responding${NC}"
    fi
else
    echo -e "  ${YELLOW}⚠ PostgreSQL container is not running${NC}"
fi

# Test LocalStack
if docker-compose ps localstack | grep -q "Up"; then
    if curl -s http://localhost:4566/_localstack/health >/dev/null 2>&1; then
        echo -e "  ${GREEN}✓ LocalStack is accessible${NC}"
    else
        echo -e "  ${RED}✗ LocalStack is not responding${NC}"
    fi
else
    echo -e "  ${YELLOW}⚠ LocalStack container is not running${NC}"
fi

# Test Redis
if docker-compose ps redis | grep -q "Up"; then
    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        echo -e "  ${GREEN}✓ Redis is accessible${NC}"
    else
        echo -e "  ${RED}✗ Redis is not responding${NC}"
    fi
else
    echo -e "  ${YELLOW}⚠ Redis container is not running${NC}"
fi

# Test Backend API
if curl -s http://localhost:3000/health >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓ Backend API is accessible${NC}"
else
    echo -e "  ${YELLOW}⚠ Backend API is not responding${NC}"
fi

echo ""
echo "=================================================="
print_status "Diagnostic Summary"

# Provide recommendations
echo ""
print_status "Recommendations:"

if ! docker info >/dev/null 2>&1; then
    echo "• Start Docker Desktop"
fi

if [ ! -f ".env" ]; then
    echo "• Create .env file: cp .env.local .env"
fi

if ! docker-compose ps | grep -q "Up"; then
    echo "• Start services: ./scripts/setup-local-dev.sh"
fi

if ! command -v aws >/dev/null 2>&1; then
    echo "• Install AWS CLI for LocalStack testing: pip install awscli"
fi

# Check for port conflicts
for port in 3000 5432 4566; do
    if lsof -i :$port >/dev/null 2>&1; then
        process=$(lsof -i :$port | tail -n 1 | awk '{print $1}')
        if [ "$process" != "docker-pr" ] && [ "$process" != "com.docke" ]; then
            echo "• Stop process using port $port: kill \$(lsof -ti :$port)"
        fi
    fi
done

echo ""
print_status "For more help, run: ./scripts/dev-helpers.sh help"
