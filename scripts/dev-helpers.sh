#!/bin/bash

# Development Helper Scripts
# Collection of useful commands for local development

# Source utility functions
source "$(dirname "$0")/utils.sh"

# Function to show help
show_help() {
    echo "Development Helper Commands:"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Available commands:"
    echo "  logs [service]     - Show logs for a service (default: all)"
    echo "  restart [service]  - Restart a service (default: backend)"
    echo "  shell [service]    - Open shell in a service container"
    echo "  db                 - Connect to PostgreSQL database"
    echo "  redis              - Connect to Redis CLI"
    echo "  s3                 - List S3 buckets in LocalStack"
    echo "  migrate            - Run database migrations"
    echo "  seed               - Seed database with test data"
    echo "  clean              - Clean up containers and volumes"
    echo "  rebuild            - Rebuild and restart services"
    echo "  status             - Show status of all services"
    echo "  test               - Run local deployment tests"
    echo ""
    echo "Examples:"
    echo "  $0 logs backend"
    echo "  $0 shell backend"
    echo "  $0 db"
    echo "  $0 migrate"
}

# Function to show service logs
show_logs() {
    local service=${1:-""}
    if [ -z "$service" ]; then
        print_status "Showing logs for all services..."
        docker-compose logs -f
    else
        print_status "Showing logs for $service..."
        docker-compose logs -f "$service"
    fi
}

# Function to restart service
restart_service() {
    local service=${1:-"backend"}
    print_status "Restarting $service..."
    docker-compose restart "$service"
    print_status "$service restarted successfully!"
}

# Function to open shell in container
open_shell() {
    local service=${1:-"backend"}
    print_status "Opening shell in $service container..."
    docker-compose exec "$service" /bin/bash || docker-compose exec "$service" /bin/sh
}

# Function to connect to database
connect_db() {
    print_status "Connecting to PostgreSQL database..."
    docker-compose exec postgres psql -U ivent_user -d ivent_dev
}

# Function to connect to Redis
connect_redis() {
    print_status "Connecting to Redis CLI..."
    docker-compose exec redis redis-cli
}

# Function to list S3 buckets
list_s3() {
    print_status "Listing S3 buckets in LocalStack..."
    aws --endpoint-url=http://localhost:4566 s3 ls
    echo ""
    print_status "Listing contents of ivent-media-dev bucket..."
    aws --endpoint-url=http://localhost:4566 s3 ls s3://ivent-media-dev/
}

# Function to run migrations
run_migrations() {
    print_status "Running database migrations..."
    docker-compose exec backend npm run migration:run
}

# Function to seed database
seed_database() {
    print_status "Seeding database with test data..."
    # Add your seeding logic here
    print_warning "Database seeding not implemented yet. Add your seeding logic to this function."
}

# Function to clean up
clean_up() {
    print_warning "This will remove all containers and volumes. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Cleaning up containers and volumes..."
        docker-compose down -v --remove-orphans
        docker system prune -f
        print_status "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to rebuild services
rebuild_services() {
    print_status "Rebuilding and restarting services..."
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    print_status "Services rebuilt and restarted!"
}

# Function to show service status
show_status() {
    print_status "Service Status:"
    docker-compose ps
    echo ""
    print_status "Container Health:"
    docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
}

# Function to run tests
run_tests() {
    print_status "Running local deployment tests..."
    ./scripts/test-local-deployment.sh
}

# Main command dispatcher
case "${1:-help}" in
    logs)
        show_logs "$2"
        ;;
    restart)
        restart_service "$2"
        ;;
    shell)
        open_shell "$2"
        ;;
    db)
        connect_db
        ;;
    redis)
        connect_redis
        ;;
    s3)
        list_s3
        ;;
    migrate)
        run_migrations
        ;;
    seed)
        seed_database
        ;;
    clean)
        clean_up
        ;;
    rebuild)
        rebuild_services
        ;;
    status)
        show_status
        ;;
    test)
        run_tests
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
