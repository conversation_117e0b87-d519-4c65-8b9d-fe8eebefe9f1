#!/bin/bash

# LocalStack Troubleshooting and Fix Script
# Usage: ./scripts/fix-localstack.sh [--force-restart] [--clean]

# Source utility functions
source "$(dirname "$0")/utils.sh"

# Parse command line arguments
FORCE_RESTART=false
CLEAN_DATA=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --force-restart)
            FORCE_RESTART=true
            shift
            ;;
        --clean)
            CLEAN_DATA=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--force-restart] [--clean]"
            echo "  --force-restart  Force restart LocalStack container"
            echo "  --clean          Clean LocalStack data and restart"
            echo "  --help           Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option $1"
            exit 1
            ;;
    esac
done

print_status "LocalStack Troubleshooting Script"

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check LocalStack container status
print_status "Checking LocalStack container status..."
if docker-compose ps localstack | grep -q "Up"; then
    print_status "LocalStack container is running"
    
    # Check if LocalStack is responding
    if curl -s http://localhost:4566/_localstack/health >/dev/null 2>&1; then
        print_status "LocalStack is responding to health checks"
        
        # Show LocalStack status
        print_status "LocalStack health status:"
        curl -s http://localhost:4566/_localstack/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:4566/_localstack/health
        
        # Test S3 service
        print_status "Testing S3 service..."
        if aws --endpoint-url=http://localhost:4566 s3 ls >/dev/null 2>&1; then
            print_status "S3 service is working"
            aws --endpoint-url=http://localhost:4566 s3 ls
        else
            print_warning "S3 service is not responding properly"
        fi
        
        if [ "$FORCE_RESTART" = false ]; then
            print_status "LocalStack appears to be working. Use --force-restart to restart anyway."
            exit 0
        fi
    else
        print_warning "LocalStack container is running but not responding to health checks"
    fi
else
    print_warning "LocalStack container is not running"
fi

# Clean data if requested
if [ "$CLEAN_DATA" = true ]; then
    print_status "Cleaning LocalStack data..."
    docker-compose down localstack
    docker volume rm ivent_api_localstack_data 2>/dev/null || true
    print_status "LocalStack data cleaned"
fi

# Restart LocalStack
print_status "Restarting LocalStack..."
docker-compose down localstack
docker-compose up -d localstack

# Wait for LocalStack to be ready with better feedback
print_status "Waiting for LocalStack to start (this may take up to 2 minutes)..."
timeout=120
counter=0
last_status=""

while [ $counter -lt $timeout ]; do
    # Check if container is running
    if ! docker-compose ps localstack | grep -q "Up"; then
        print_error "LocalStack container failed to start"
        print_error "Container logs:"
        docker-compose logs --tail=20 localstack
        exit 1
    fi
    
    # Check health endpoint
    if curl -s http://localhost:4566/_localstack/health >/dev/null 2>&1; then
        echo ""
        print_status "LocalStack is ready!"
        break
    fi
    
    # Show progress every 10 seconds
    if [ $((counter % 10)) -eq 0 ] && [ $counter -gt 0 ]; then
        echo ""
        print_status "Still waiting... ($counter/$timeout seconds)"
        
        # Try to get more detailed status
        status=$(curl -s http://localhost:4566/_localstack/health 2>/dev/null || echo "No response")
        if [ "$status" != "$last_status" ] && [ "$status" != "No response" ]; then
            print_status "Status update: $status"
            last_status="$status"
        fi
    else
        echo -n "."
    fi
    
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    echo ""
    print_error "LocalStack failed to start within $timeout seconds"
    print_error "Container status:"
    docker-compose ps localstack
    print_error "Recent logs:"
    docker-compose logs --tail=30 localstack
    
    print_error "Troubleshooting suggestions:"
    echo "1. Check if port 4566 is already in use: lsof -i :4566"
    echo "2. Try cleaning data: $0 --clean"
    echo "3. Check Docker resources (memory/CPU)"
    echo "4. Try using a different LocalStack version"
    echo "5. Check Docker logs: docker-compose logs localstack"
    
    exit 1
fi

# Test LocalStack services
print_status "Testing LocalStack services..."

# Test S3
print_test "Testing S3 service..."
if aws --endpoint-url=http://localhost:4566 s3 ls >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓ S3 service is working${NC}"
else
    echo -e "  ${RED}✗ S3 service failed${NC}"
fi

# Test SSM
print_test "Testing SSM service..."
if aws --endpoint-url=http://localhost:4566 ssm describe-parameters >/dev/null 2>&1; then
    echo -e "  ${GREEN}✓ SSM service is working${NC}"
else
    echo -e "  ${RED}✗ SSM service failed${NC}"
fi

# Setup S3 buckets
print_status "Setting up S3 buckets..."
if ./scripts/setup-local-s3.sh; then
    print_status "S3 buckets setup completed successfully!"
else
    print_warning "S3 bucket setup failed. You may need to run it manually later."
fi

# Final status
print_status "LocalStack troubleshooting completed!"
print_status "LocalStack is available at: http://localhost:4566"
print_status "Health check: curl http://localhost:4566/_localstack/health"
print_status "S3 test: aws --endpoint-url=http://localhost:4566 s3 ls"
