#!/usr/bin/env ts-node

import * as fs from 'fs';
import * as path from 'path';

// --- Get CLI args ---
const [, , ENUM_FOLDER_ARG, MIGRATIONS_FOLDER_ARG, MIGRATION_PREFIX_ARG] = process.argv;

if (!ENUM_FOLDER_ARG || !MIGRATIONS_FOLDER_ARG || !MIGRATION_PREFIX_ARG) {
  console.error('Usage: ts-node generate-enum-migration.ts <ENUM_FOLDER> <MIGRATIONS_FOLDER> <MIGRATION_PREFIX>');
  process.exit(1);
}

const ENUM_FOLDER = path.resolve(ENUM_FOLDER_ARG);
const MIGRATIONS_FOLDER = path.resolve(MIGRATIONS_FOLDER_ARG);
const MIGRATION_PREFIX = MIGRATION_PREFIX_ARG;

// --- Generate timestamp and migration name ---
const timestamp = Date.now();
const migrationName = `${MIGRATION_PREFIX}${timestamp}`;
const fileName = `${timestamp}-${MIGRATION_PREFIX}.ts`;

// --- Read enum files ---
const enumFiles = fs.readdirSync(ENUM_FOLDER).filter((f) => f.endsWith('.ts') && !f.includes('index.ts'));

// --- Generate import statements and up/down queries ---
const upQueries: string[] = [];
const downQueries: string[] = [];

function toSnakeCase(name: string) {
  return name
    .replace(/([a-z0-9])([A-Z])/g, '$1_$2') // insert _ between camelCase boundaries
    .replace(/([A-Z])([A-Z][a-z])/g, '$1_$2') // handle consecutive capitals
    .toLowerCase();
}

enumFiles.forEach((file) => {
  const fullPath = path.join(ENUM_FOLDER, file);

  const exportedEnums = require(fullPath); // Using require to access all exports
  for (const [enumName, enumValues] of Object.entries(exportedEnums)) {
    // Skip non-enum exports
    if (!enumValues || typeof enumValues !== 'object') continue;

    upQueries.push(
      `await queryRunner.query(\`CREATE TYPE "public"."${toSnakeCase(enumName)}" AS ENUM(${Object.values(enumValues)
        .map((v) => `'${v}'`)
        .join(', ')})\`);`,
    );

    downQueries.push(`await queryRunner.query(\`DROP TYPE IF EXISTS "public"."${toSnakeCase(enumName)}"\`);`);
  }
});

// --- Migration template ---
const template = `
import { MigrationInterface, QueryRunner } from "typeorm";

export class ${migrationName} implements MigrationInterface {
    name = '${migrationName}'

    public async up(queryRunner: QueryRunner): Promise<void> {
        ${upQueries.join('\n        ')}
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        ${downQueries.join('\n        ')}
    }
}
`;

// --- Write migration file ---
fs.mkdirSync(MIGRATIONS_FOLDER, { recursive: true });
fs.writeFileSync(path.join(MIGRATIONS_FOLDER, fileName), template.trim());

console.log(`Migration generated: ${fileName}`);