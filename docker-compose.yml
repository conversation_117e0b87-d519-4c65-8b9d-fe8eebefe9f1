version: "3.9"

services:
  # PostgreSQL Database - Production-like setup
  postgres:
    image: postgres:15-alpine
    container_name: ivent_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ivent_dev}
      POSTGRES_USER: ${POSTGRES_USER:-ivent_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-dev_password}
      # Additional PostgreSQL configurations for development
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # Mount initialization scripts if needed
      - ./docker/postgres/init:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-ivent_user} -d ${POSTGRES_DB:-ivent_dev}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - ivent_network
    restart: unless-stopped

  # LocalStack for AWS services simulation
  localstack:
    image: localstack/localstack:latest
    container_name: ivent_localstack
    environment:
      - SERVICES=s3,ssm,secretsmanager,sns,ses
      - DEBUG=0
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
      - LOCALSTACK_HOST=localstack
      - EDGE_PORT=4566
      - AWS_DEFAULT_REGION=eu-central-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      # Performance optimizations
      - SKIP_INFRA_DOWNLOADS=1
      - LAMBDA_EXECUTOR=local
      - KINESIS_ERROR_PROBABILITY=0.0
      - DISABLE_CORS_CHECKS=1
      - DISABLE_CUSTOM_CORS_S3=1
      - EAGER_SERVICE_LOADING=0
    ports:
      - "4566:4566"
    volumes:
      - localstack_data:/tmp/localstack
      - /var/run/docker.sock:/var/run/docker.sock
      - ./docker/localstack/init:/etc/localstack/init/ready.d:ro
    networks:
      - ivent_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 60s

  # Application - Development Mode
  backend:
    container_name: ivent_api
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    env_file:
      - .env.local
    environment:
      # Override for local development
      NODE_ENV: development
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      # LocalStack endpoints
      AWS_ENDPOINT_URL: http://localstack:4566
      AWS_ACCESS_KEY_ID: test
      AWS_SECRET_ACCESS_KEY: test
      AWS_DEFAULT_REGION: eu-central-1
      AWS_REGION: eu-central-1
      # Development specific
      CHOKIDAR_USEPOLLING: "true"
      WATCHPACK_POLLING: "true"
    ports:
      - "${SERVER_TCP_PORT:-3000}:3000"
      - "9229:9229"  # Debug port
    depends_on:
      postgres:
        condition: service_healthy
      localstack:
        condition: service_healthy
    volumes:
      # Source code for hot reloading
      - ./src:/app/src:cached
      - ./package.json:/app/package.json:ro
      - ./package-lock.json:/app/package-lock.json:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - ./tsconfig.build.json:/app/tsconfig.build.json:ro
      - ./nest-cli.json:/app/nest-cli.json:ro
      - ./swagger-*.css:/app/:ro
      # Node modules volume for better performance
      - node_modules:/app/node_modules
      # SSL certificate if needed
      - ./ca.pem:/app/certs/ca.pem:ro
    networks:
      - ivent_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Redis for caching (optional but recommended for development)
  redis:
    image: redis:7-alpine
    container_name: ivent_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ivent_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ivent_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
      PGADMIN_CONFIG_SERVER_MODE: "False"
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - ivent_network
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  localstack_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local
  node_modules:
    driver: local

networks:
  ivent_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
