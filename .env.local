# =============================================================================
# LOCAL DEVELOPMENT ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains environment variables for local Docker development
# Copy this to .env and modify values as needed

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
ENVIRONMENT=local
SERVER_HOST=0.0.0.0
SERVER_TCP_PORT=3000
USE_SWAGGER=true

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL in Docker)
# =============================================================================
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=ivent_user
POSTGRES_PASSWORD=dev_password_123
POSTGRES_DB=ivent_dev

# =============================================================================
# AWS CONFIGURATION (LocalStack)
# =============================================================================
AWS_REGION=eu-central-1
AWS_DEFAULT_REGION=eu-central-1
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_ENDPOINT_URL=http://localstack:4566
AWS_S3_BUCKET_NAME=ivent-media-dev
AWS_S3_REGION=eu-central-1

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=local-development-jwt-secret-key-change-in-production
JWT_EXPIRES_IN=7d

# =============================================================================
# REDIS CONFIGURATION (Optional)
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# =============================================================================
# EXTERNAL SERVICES (Development/Testing)
# =============================================================================
# Firebase (if used - use test project for local development)
FIREBASE_PROJECT_ID=ivent-local-dev
FIREBASE_PRIVATE_KEY=your_test_private_key
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_STORAGE_BUCKET=ivent-local-dev.appspot.com

# Twilio (if used - use test credentials)
TWILIO_ACCOUNT_SID=test_account_sid
TWILIO_AUTH_TOKEN=test_auth_token

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# pgAdmin configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123

# Development debugging
DEBUG=*
LOG_LEVEL=debug

# Hot reloading configuration
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true

# =============================================================================
# LOCALSTACK SPECIFIC CONFIGURATION
# =============================================================================
# These are used by LocalStack to simulate AWS services
LOCALSTACK_HOST=localstack
EDGE_PORT=4566

# =============================================================================
# DEVELOPMENT FLAGS
# =============================================================================
# Enable/disable features for local development
ENABLE_CORS=true
ENABLE_LOGGING=true
ENABLE_SWAGGER_UI=true
ENABLE_DEBUG_ROUTES=true

# Database synchronization (only for development)
TYPEORM_SYNCHRONIZE=false
TYPEORM_LOGGING=true
TYPEORM_DROP_SCHEMA=false

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
# Test database (if running tests in containers)
TEST_POSTGRES_HOST=postgres
TEST_POSTGRES_PORT=5432
TEST_POSTGRES_USER=ivent_user
TEST_POSTGRES_PASSWORD=dev_password_123
TEST_POSTGRES_DB=ivent_test

# =============================================================================
# PERFORMANCE TUNING FOR DEVELOPMENT
# =============================================================================
# Node.js memory settings for development
NODE_OPTIONS=--max-old-space-size=4096

# TypeScript compilation settings
TS_NODE_TRANSPILE_ONLY=true
TS_NODE_FILES=true
