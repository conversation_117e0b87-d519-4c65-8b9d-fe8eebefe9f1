#!/bin/bash

# LocalStack S3 initialization script
# This script runs automatically when LocalStack starts up

set -e

echo "Setting up LocalStack S3 buckets..."

# Set AWS CLI to use LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=eu-central-1

BUCKET_NAME=${AWS_S3_BUCKET_NAME:-ivent-media-dev}

# Wait for LocalStack to be ready
echo "Waiting for LocalStack to be ready..."
until curl -s http://localhost:4566/_localstack/health > /dev/null; do
  echo "Waiting for LocalStack..."
  sleep 2
done

echo "LocalStack is ready. Creating S3 bucket: $BUCKET_NAME"

# Create the main media bucket
aws --endpoint-url=http://localhost:4566 s3 mb "s3://$BUCKET_NAME" || echo "Bucket may already exist"

# Create folder structure
echo "Creating folder structure in S3 bucket..."
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "vibes/" --content-length 0
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "thumbnails/" --content-length 0
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "thumbnails/vibes/" --content-length 0
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "ivents/" --content-length 0
aws --endpoint-url=http://localhost:4566 s3api put-object --bucket "$BUCKET_NAME" --key "memories/" --content-length 0

# Set CORS configuration for the bucket
echo "Setting CORS configuration..."
cat > /tmp/cors-config.json << EOF
{
    "CORSRules": [
        {
            "AllowedHeaders": ["*"],
            "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
            "AllowedOrigins": ["*"],
            "ExposeHeaders": ["ETag"],
            "MaxAgeSeconds": 3000
        }
    ]
}
EOF

aws --endpoint-url=http://localhost:4566 s3api put-bucket-cors --bucket "$BUCKET_NAME" --cors-configuration file:///tmp/cors-config.json

# Set bucket policy for public read access (development only)
echo "Setting bucket policy for development..."
cat > /tmp/bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::$BUCKET_NAME/*"
        }
    ]
}
EOF

aws --endpoint-url=http://localhost:4566 s3api put-bucket-policy --bucket "$BUCKET_NAME" --policy file:///tmp/bucket-policy.json

# List buckets to verify
echo "Verifying bucket creation..."
aws --endpoint-url=http://localhost:4566 s3 ls

echo "LocalStack S3 setup completed successfully!"
echo "Bucket: $BUCKET_NAME"
echo "Endpoint: http://localhost:4566"

# Clean up
rm -f /tmp/cors-config.json /tmp/bucket-policy.json
