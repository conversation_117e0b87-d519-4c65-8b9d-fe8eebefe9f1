import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum, PageMembershipStatusEnum, UserRelationshipStatusEnum } from 'src/enums';

export class CollabratorListItem {
  @ApiProperty({
    description: 'UUID of the collaborator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  collabId!: string;

  @ApiProperty({
    description: 'Name of the collaborator',
    example: 'Photography Club Istanbul or john_doe',
  })
  collabName!: string;

  @ApiProperty({
    enum: AccountTypeEnum,
    enumName: 'AccountTypeEnum',
    description: 'Type of the collaborator account (user or page)',
    example: AccountTypeEnum.USER,
  })
  collabType!: AccountTypeEnum;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the collaborator image',
    example: 'https://example.com/collaborator-image.jpg',
    format: 'url',
  })
  collabImageUrl?: string | null;

  @ApiProperty({
    enum: PageMembershipStatusEnum,
    enumName: 'PageMembershipStatusEnum',
    description: 'Membership status of the collaborator in the page',
    example: PageMembershipStatusEnum.ADMIN,
  })
  pageMembershipStatus!: PageMembershipStatusEnum;

  @ApiProperty({
    enum: UserRelationshipStatusEnum,
    enumName: 'UserRelationshipStatusEnum',
    description: 'Relationship status of the collaborator with the current user',
    example: UserRelationshipStatusEnum.ACCEPTED,
  })
  relationshipStatus!: UserRelationshipStatusEnum;
}
