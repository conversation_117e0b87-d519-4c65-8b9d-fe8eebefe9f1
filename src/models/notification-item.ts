import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum, NotificationTypeEnum } from 'src/enums';

export class NotificationItem {
  @ApiProperty({
    enum: NotificationTypeEnum,
    enumName: 'NotificationTypeEnum',
    description: 'Type of notification',
    example: NotificationTypeEnum.ARKADASLIK_ISTEGI_GONDERDI,
  })
  notificationType!: NotificationTypeEnum;

  @ApiProperty({
    description: 'Unique identifier of the notification',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  notificationId!: string;

  @ApiProperty({
    description: 'Timestamp when the notification was created, in ISO 8601 date-time format',
    example: '2023-12-01T10:30:00Z',
  })
  createdAt!: string;

  @ApiProperty({
    enum: AccountTypeEnum,
    enumName: 'AccountTypeEnum',
    description: 'Type of account that triggered the notification (page or user)',
    example: AccountTypeEnum.USER,
  })
  accountType!: AccountTypeEnum;

  @ApiProperty({
    description: 'UUID of the account that triggered the notification',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  accountId!: string;

  @ApiProperty({
    description: 'Name of the account that triggered the notification',
    example: 'irem_basoglu',
  })
  accountName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Avatar URL of the account that triggered the notification',
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  accountAvatarUrl?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Type of content related to the notification',
    example: 'vibe',
  })
  contentType?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'UUID of the content related to the notification',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  contentId?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Thumbnail URL of the content',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  contentThumbnailUrl?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Name or title of the content',
    example: 'Teoman Concert Vibe',
  })
  contentName?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Description of the content item',
    example: '3 Memories',
  })
  contentItem?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Type of action that triggered the notification',
    example: 'memory_added',
  })
  actionType?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'UUID of the item to navigate to when notification is tapped',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  actionId?: string | null;
}
