import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Ivent, Page, User } from 'src/entities';
import { AccountTypeEnum } from 'src/enums';
import { hydrateComputedList } from 'src/utils/hydrate-computed-list';
import { stringToBooleanTransformer } from 'src/utils/value-transformers';
import { DataSource } from 'typeorm';
import { FeedParams, MapParams, SearchAccountParams, SearchIventParams } from './models/home.params';
import { FeedReturn, MapReturn, SearchAccountReturn, SearchIventReturn } from './models/home.returns';

@Injectable()
export class HomeService {
  constructor(private dataSource: DataSource) {}

  private getDateRange(
    dateType: string,
    startDate: string | null,
    endDate: string | null,
  ): { startDateAdjusted: string | undefined; endDateAdjusted: string | undefined } {
    let startDateAdjusted: string | undefined;
    let endDateAdjusted: string | undefined;

    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      switch (dateType) {
        case 'today':
          startDateAdjusted = today.toISOString().split('T')[0];
          endDateAdjusted = today.toISOString().split('T')[0] + 'T23:59:59.999Z';
          break;

        case 'tomorrow':
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);
          startDateAdjusted = tomorrow.toISOString().split('T')[0];
          endDateAdjusted = tomorrow.toISOString().split('T')[0] + 'T23:59:59.999Z';
          break;

        case 'this_week':
          const startOfWeek = new Date(today);
          startOfWeek.setDate(today.getDate() - today.getDay() + 1); // Monday
          const endOfWeek = new Date(startOfWeek);
          endOfWeek.setDate(startOfWeek.getDate() + 6); // Sunday
          startDateAdjusted = startOfWeek.toISOString().split('T')[0];
          endDateAdjusted = endOfWeek.toISOString().split('T')[0] + 'T23:59:59.999Z';
          break;

        case 'next_week':
          const nextWeekStart = new Date(today);
          nextWeekStart.setDate(today.getDate() + (7 - today.getDay() + 1)); // Next Monday
          const nextWeekEnd = new Date(nextWeekStart);
          nextWeekEnd.setDate(nextWeekStart.getDate() + 6); // Next Sunday
          startDateAdjusted = nextWeekStart.toISOString().split('T')[0];
          endDateAdjusted = nextWeekEnd.toISOString().split('T')[0] + 'T23:59:59.999Z';
          break;

        case 'this_month':
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
          startDateAdjusted = startOfMonth.toISOString().split('T')[0];
          endDateAdjusted = endOfMonth.toISOString().split('T')[0] + 'T23:59:59.999Z';
          break;

        case 'this_summer':
          const summerStart = new Date(now.getFullYear(), 5, 1); // June 1st
          const summerEnd = new Date(now.getFullYear(), 7, 31); // August 31st
          startDateAdjusted = summerStart.toISOString().split('T')[0];
          endDateAdjusted = summerEnd.toISOString().split('T')[0] + 'T23:59:59.999Z';
          break;

        case 'holiday':
          const holidayStart = new Date(now.getFullYear(), 11, 23); // December 23rd
          const holidayEnd = new Date(now.getFullYear(), 11, 31); // December 31st
          startDateAdjusted = holidayStart.toISOString().split('T')[0];
          endDateAdjusted = holidayEnd.toISOString().split('T')[0] + 'T23:59:59.999Z';
          break;

        case 'range':
          if (startDate) {
            const parsedStartDate = new Date(startDate);
            if (isNaN(parsedStartDate.getTime())) {
              throw new HttpException('Invalid start date format.', HttpStatus.BAD_REQUEST);
            }
            startDateAdjusted = parsedStartDate.toISOString().split('T')[0];
          }
          if (endDate) {
            const parsedEndDate = new Date(endDate);
            if (isNaN(parsedEndDate.getTime())) {
              throw new HttpException('Invalid end date format.', HttpStatus.BAD_REQUEST);
            }
            endDateAdjusted = parsedEndDate.toISOString().split('T')[0] + 'T23:59:59.999Z';
          }
          break;

        default:
          // For unknown date types, return undefined (no date filtering)
          startDateAdjusted = undefined;
          endDateAdjusted = undefined;
          break;
      }
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error processing date range.', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return { startDateAdjusted, endDateAdjusted };
  }

  async feed(feedParams: FeedParams): Promise<FeedReturn> {
    const {
      sessionId,

      dateType,
      startDate,
      endDate,
      categories,
      locationCoeff,
      latitude,
      longitude,
      q,
      limit,
      page,
    } = feedParams;

    const { startDateAdjusted, endDateAdjusted } = this.getDateRange(dateType, startDate, endDate);

    // Build query using TypeORM query builder
    const queryBuilder = this.dataSource
      .getRepository(Ivent)
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoinAndSelect('i.creator_page', 'p')
      .leftJoinAndSelect('i.creator_user', 'u')
      .leftJoinAndSelect('i.creator_distributor', 'dis')
      .leftJoin('i.dates', 'd')
      .leftJoin('i.favorited_by', 'uf', 'uf.user_id = :sessionId', { sessionId })
      .addSelect('CASE WHEN uf.user_id IS NOT NULL THEN TRUE ELSE FALSE END', 'is_favorited');

    // Add date filter
    if (startDateAdjusted && endDateAdjusted) {
      queryBuilder.where('d.ivent_date::date BETWEEN :startDate AND :endDate', {
        startDate: startDateAdjusted,
        endDate: endDateAdjusted,
      });
    } else {
      queryBuilder.where('d.ivent_date::date > NOW()::date');
    }

    // Add search query filter
    if (q && q.trim()) {
      queryBuilder.andWhere('(i.ivent_name ILIKE :searchQuery OR l.location_name ILIKE :searchQuery)', {
        searchQuery: `${q.trim()}%`,
      });
    }

    // Add category filter
    if (categories && categories.length > 0) {
      queryBuilder.andWhere('i.category_tag_id IN (:...categories)', { categories });
    }

    // Add geographic filter
    if (latitude && longitude) {
      queryBuilder.andWhere(
        'ST_Distance(l.geom::geography, ST_MakePoint(:longitude, :latitude)::geography) < 10000',
        { latitude, longitude },
      );
    }

    // Add pagination and ordering
    queryBuilder
      .orderBy('i.created_at', 'DESC')
      .limit(limit)
      .offset(limit * (page - 1));

    const { entities, raw } = await queryBuilder.getRawAndEntities();

    // Transform data using hydrateComputedList for computed fields
    const ivents = hydrateComputedList(entities, raw, [
      { field: 'is_favorited', transformer: stringToBooleanTransformer },
    ]);

    return {
      ivents: ivents.map((ivent) => ({
        iventId: ivent.id,
        iventName: ivent.ivent_name,
        thumbnailUrl: ivent.thumbnail_url,
        locationName: ivent.location!.location_name,
        creatorId: ivent.creator_id!,
        creatorType: ivent.creator_type,
        creatorUsername: ivent.creator_name!,
        creatorImageUrl: ivent.creator_image_url,
        isFavorited: ivent.is_favorited!,
      })),
      iventCount: ivents.length,
    };
  }

  async map(mapParams: MapParams): Promise<MapReturn> {
    const { sessionId, startDate, endDate, latStart, latEnd, lngStart, lngEnd, limit } = mapParams;

    // Input validation
    if (limit <= 0 || limit > 1000) {
      throw new HttpException('Limit must be between 1 and 1000.', HttpStatus.BAD_REQUEST);
    }

    if (latStart < -90 || latStart > 90 || latEnd < -90 || latEnd > 90) {
      throw new HttpException('Latitude must be between -90 and 90.', HttpStatus.BAD_REQUEST);
    }

    if (lngStart < -180 || lngStart > 180 || lngEnd < -180 || lngEnd > 180) {
      throw new HttpException('Longitude must be between -180 and 180.', HttpStatus.BAD_REQUEST);
    }

    // Build query using TypeORM query builder
    const queryBuilder = this.dataSource
      .getRepository(Ivent)
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoin('i.dates', 'd')
      .where('ST_Within(l.geom::geometry, ST_MakeEnvelope(:latStart, :lngStart, :latEnd, :lngEnd, 4326)::geometry)', {
        latStart,
        lngStart,
        latEnd,
        lngEnd,
      })
      .andWhere('d.ivent_date::date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });

    queryBuilder.limit(limit);

    const ivents = await queryBuilder.getMany();

    return {
      ivents: ivents.map((result) => ({
        iventId: result.id,
        latitude: result.latitude,
        longitude: result.longitude,
      })),
      iventCount: ivents.length,
    };
  }

  async searchIvent(searchIventParams: SearchIventParams): Promise<SearchIventReturn> {
    const { sessionId, q, limit, page } = searchIventParams;

    // Input validation
    if (!q || q.trim().length === 0) {
      throw new HttpException('Search query cannot be empty.', HttpStatus.BAD_REQUEST);
    }

    if (limit <= 0 || limit > 100) {
      throw new HttpException('Limit must be between 1 and 100.', HttpStatus.BAD_REQUEST);
    }

    if (page <= 0) {
      throw new HttpException('Page must be greater than 0.', HttpStatus.BAD_REQUEST);
    }

    const searchQuery = q.trim();

    // Build query using TypeORM query builder
    const queryBuilder = this.dataSource
      .getRepository(Ivent)
      .createQueryBuilder('i')
      .leftJoinAndSelect('i.location', 'l')
      .leftJoinAndSelect('i.creator_page', 'p')
      .leftJoinAndSelect('i.creator_user', 'u')
      .leftJoinAndSelect('i.creator_distributor', 'dis')
      .where('(i.ivent_name ILIKE :searchQuery OR l.location_name ILIKE :searchQuery)', {
        searchQuery: `${searchQuery}%`,
      })
      .leftJoin('user_favorites', 'uf', 'uf.favorited_ivent_id = i.id AND uf.user_id = :sessionId', { sessionId })
      .addSelect('CASE WHEN uf.user_id IS NOT NULL THEN TRUE ELSE FALSE END', 'is_favorited');

    // Add pagination and ordering
    queryBuilder
      .orderBy('i.created_at', 'DESC')
      .limit(limit)
      .offset(limit * (page - 1));

    const { entities, raw } = await queryBuilder.getRawAndEntities();

    // Transform data using hydrateComputedList for computed fields
    const ivents = hydrateComputedList(entities, raw, [
      { field: 'is_favorited', transformer: stringToBooleanTransformer },
    ]);

    return {
      ivents: ivents.map((ivent) => ({
        iventId: ivent.id,
        iventName: ivent.ivent_name,
        thumbnailUrl: ivent.thumbnail_url,
        locationName: ivent.location!.location_name,
        creatorId: ivent.creator_id!,
        creatorType: ivent.creator_type,
        creatorUsername: ivent.creator_name!,
        creatorImageUrl: ivent.creator_image_url,
        isFavorited: ivent.is_favorited!,
      })),
      iventCount: ivents.length,
    };
  }

  async searchAccount(searchAccountParams: SearchAccountParams): Promise<SearchAccountReturn> {
    const { sessionId, q, limit, page } = searchAccountParams;

    // Input validation
    if (!q || q.trim().length === 0) {
      throw new HttpException('Search query cannot be empty.', HttpStatus.BAD_REQUEST);
    }

    if (limit <= 0 || limit > 100) {
      throw new HttpException('Limit must be between 1 and 100.', HttpStatus.BAD_REQUEST);
    }

    if (page <= 0) {
      throw new HttpException('Page must be greater than 0.', HttpStatus.BAD_REQUEST);
    }

    const searchQuery = q.trim();

    // Search pages
    const pageQueryBuilder = this.dataSource
      .getRepository(Page)
      .createQueryBuilder('p')
      .select(['p.id', 'p.page_name', 'p.thumbnail_url'])
      .where('p.page_name ILIKE :searchQuery', { searchQuery: `${searchQuery}%` });

    // Search users
    const userQueryBuilder = this.dataSource
      .getRepository(User)
      .createQueryBuilder('u')
      .select(['u.id', 'u.username', 'u.avatar_url'])
      .where('u.username ILIKE :searchQuery', { searchQuery: `${searchQuery}%` });

    // Execute both queries
    const [pages, users] = await Promise.all([pageQueryBuilder.getMany(), userQueryBuilder.getMany()]);

    // Combine and format results
    const accounts = [
      ...pages.map((page) => ({
        accountId: page.id,
        accountName: page.page_name,
        accountType: AccountTypeEnum.PAGE,
        accountImageUrl: page.thumbnail_url,
      })),
      ...users.map((user) => ({
        accountId: user.id,
        accountName: user.username,
        accountType: AccountTypeEnum.USER,
        accountImageUrl: user.avatar_url,
      })),
    ];

    // Apply pagination manually since we're combining results
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedAccounts = accounts.slice(startIndex, endIndex);

    return {
      accounts: paginatedAccounts,
      accountCount: paginatedAccounts.length,
    };
  }
}
