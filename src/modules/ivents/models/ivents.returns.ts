import { ApiProperty } from '@nestjs/swagger';
import { IventCreatorTypeEnum, IventViewTypeEnum } from 'src/enums';
import { IventCardItem, IventListItem } from 'src/models';

export class CreateIventReturn {
  @ApiProperty({
    description: 'UUID of the newly created ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;
}

export class GetBannerByIventIdReturn {
  @ApiProperty({
    type: [IventCardItem],
    description: 'Array of ivent card items for banner display',
    example: [],
  })
  ivents!: IventCardItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of ivents in the banner',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class GetIventPageByIventIdReturn {
  @ApiProperty({
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the ivent thumbnail image',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl?: string | null;

  @ApiProperty({
    description: 'UUID of the location',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  locationId!: string;

  @ApiProperty({
    description: 'Mapbox place ID for the location',
    example: 'address.1234567890',
  })
  mapboxId!: string;

  @ApiProperty({
    description: 'Name of the location',
    example: 'Central Park',
  })
  locationName!: string;

  @ApiProperty({
    description: 'List of dates for the ivent, in ISO 8601 date-time format',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  dates!: string[];

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Detailed description of the ivent',
    example: 'Join us for an amazing outdoor photography workshop!',
  })
  description?: string | null;

  @ApiProperty({
    description: 'Name of the category tag',
    example: 'Photography',
  })
  categoryTag!: string;

  @ApiProperty({
    description: 'List of hobby tags associated with the ivent',
    example: ['Photography', 'Outdoor'],
  })
  tagNames!: string[];

  @ApiProperty({
    description: 'UUID of the ivent creator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  creatorId!: string;

  @ApiProperty({
    enum: IventCreatorTypeEnum,
    enumName: 'IventCreatorTypeEnum',
    description: 'Type of the ivent creator',
    example: IventCreatorTypeEnum.USER,
  })
  creatorType!: IventCreatorTypeEnum;

  @ApiProperty({
    description: 'Username of the ivent creator',
    example: 'john_doe',
  })
  creatorUsername!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the ivent creator image',
    example: 'https://example.com/creator-image.jpg',
    format: 'url',
  })
  creatorImageUrl?: string | null;

  @ApiProperty({
    description: 'List of collaborator names, either page names or usernames',
    example: ['Photography Club Istanbul', 'jane_doe'],
  })
  collabNames!: string[];

  @ApiProperty({
    type: 'integer',
    description: 'Number of collaborators',
    example: 2,
    minimum: 0,
  })
  collabCount!: number;

  @ApiProperty({
    type: 'string',
    isArray: true,
    nullable: true,
    required: false,
    description: 'List of member first names',
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[] | null;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', nullable: true },
    format: 'url',
    description: 'List of member avatar URLs',
    example: ['https://example.com/avatar1.jpg', 'https://example.com/avatar2.jpg'],
  })
  memberAvatarUrls!: (string | null)[];

  @ApiProperty({
    type: 'integer',
    description: 'Number of members',
    example: 2,
    minimum: 0,
  })
  memberCount!: number;

  @ApiProperty({
    type: 'boolean',
    nullable: true,
    required: false,
    description: 'Whether the ivent is favorited by the current user',
    example: true,
  })
  isFavorited?: boolean | null;

  @ApiProperty({
    type: 'integer',
    nullable: true,
    required: false,
    description: 'Number of favorites the ivent has',
    example: 10,
    minimum: 0,
  })
  favoriteCount?: number | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to Google Forms for registration',
    example: 'https://forms.google.com/...',
    format: 'url',
  })
  googleFormsUrl?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Instagram username for the ivent',
    example: 'my_ivent_account',
  })
  instagramUsername?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'WhatsApp group URL',
    example: 'https://chat.whatsapp.com/...',
    format: 'url',
  })
  whatsappUrl?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'WhatsApp contact number',
    example: '+90(555)1234567',
  })
  whatsappNumber?: string | null;

  @ApiProperty({
    type: 'boolean',
    nullable: true,
    required: false,
    description: 'Whether the WhatsApp URL should be kept private',
    example: false,
  })
  isWhatsappUrlPrivate?: boolean | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Phone number for calls',
    example: '+90(555)1234567',
  })
  callNumber?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Website URL for the ivent',
    example: 'https://myivent.com',
    format: 'url',
  })
  websiteUrl?: string | null;

  @ApiProperty({
    enum: IventViewTypeEnum,
    enumName: 'IventViewTypeEnum',
    description: 'View type of the ivent for the current user',
    example: IventViewTypeEnum.DEFAULT,
  })
  viewType!: IventViewTypeEnum;
}

export class GetLatestIventsReturn {
  @ApiProperty({
    type: [IventListItem],
    description: 'List of latest ivents',
    example: [],
  })
  ivents!: IventListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of latest ivents',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class GetSuggestedImagesReturn {
  @ApiProperty({
    format: 'url',
    description: 'List of suggested image URLs',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
  })
  imageUrls!: string[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of suggested images',
    example: 2,
    minimum: 0,
  })
  imageCount!: number;
}

export class GetUpcomingIventReturn {
  @ApiProperty({
    description: 'UUID of the upcoming ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    description: 'Name of the upcoming ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    description: 'List of dates for the ivent, in ISO 8601 date-time format',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  dates!: string[];

  @ApiProperty({
    description: "List of member's first names in the ivent",
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[];

  @ApiProperty({
    type: 'integer',
    description: 'Number of members in the ivent',
    example: 10,
    minimum: 0,
  })
  memberCount!: number;
}
