import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsISO8601,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  IsUrl,
  Matches,
  ValidateNested,
} from 'class-validator';
import { AccountTypeEnum, IventPrivacyEnum } from 'src/enums';

export class CollabDto {
  @ApiProperty({
    description: 'UUID of the collaborator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'Collaborator ID must be a valid UUID v4' })
  id!: string;

  @ApiProperty({
    enum: AccountTypeEnum,
    enumName: 'AccountTypeEnum',
    description: 'Type of the collaborator account',
    example: AccountTypeEnum.USER,
  })
  @IsEnum(AccountTypeEnum, { message: 'Type must be a valid AccountTypeEnum value' })
  type!: AccountTypeEnum;
}

export class CreateIventDto {
  @ApiProperty({
    enum: AccountTypeEnum,
    enumName: 'AccountTypeEnum',
    description: 'Type of the creator account',
    example: AccountTypeEnum.USER,
  })
  @IsEnum(AccountTypeEnum, { message: 'Creator type must be a valid AccountTypeEnum value' })
  creatorType!: AccountTypeEnum;

  @ApiProperty({
    description: 'Ivent name can only contain letters, numbers, underscores, and hyphens',
    example: 'My Awesome Ivent',
    minLength: 3,
    maxLength: 200,
  })
  @IsString()
  @Matches(/^[\p{L}0-9 _\-]{3,200}$/u, {
    message: 'Ivent name can only contain letters, numbers, spaces, underscores, and hyphens (3-200 characters)',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to the ivent thumbnail image',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  @IsUrl({}, { message: 'Thumbnail URL must be a valid URL' })
  @IsOptional()
  thumbnailUrl?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Base64 encoded thumbnail image buffer',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...',
  })
  @IsString()
  @IsOptional()
  thumbnailBuffer?: string | null;

  @ApiProperty({
    description: 'Array of date strings in ISO 8601 date-time format',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  @IsArray()
  @IsISO8601({}, { each: true, message: 'Each date must be a valid ISO 8601 date-time string' })
  dates!: string[];

  @ApiProperty({
    description: 'Mapbox place ID for location',
    example: 'address.1234567890',
  })
  @IsString()
  mapboxId!: string;

  @ApiProperty({
    type: 'double',
    description: 'Latitude coordinate of the ivent location',
    example: 41.0082,
    minimum: -90,
    maximum: 90,
  })
  @IsNumber({}, { message: 'Latitude must be a valid number' })
  latitude!: number;

  @ApiProperty({
    type: 'double',
    description: 'Longitude coordinate of the ivent location',
    example: 28.9784,
    minimum: -180,
    maximum: 180,
  })
  @IsNumber({}, { message: 'Longitude must be a valid number' })
  longitude!: number;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Detailed description of the ivent',
    example: 'Join us for an amazing outdoor photography workshop!',
  })
  @IsString()
  @IsOptional()
  description?: string | null;

  @ApiProperty({
    description: 'UUID of the category tag for this ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'Category tag ID must be a valid UUID v4' })
  categoryTagId!: string;

  @ApiProperty({
    format: 'uuid',
    description: 'Array of hobby tag UUIDs associated with this ivent',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each hobby tag must be a valid UUID v4' })
  tagIds!: string[];

  @ApiProperty({
    enum: IventPrivacyEnum,
    enumName: 'IventPrivacyEnum',
    description: 'Privacy setting for the ivent',
    example: IventPrivacyEnum.PUBLIC,
  })
  @IsEnum(IventPrivacyEnum, { message: 'Privacy must be a valid IventPrivacyEnum value' })
  privacy!: IventPrivacyEnum;

  @ApiProperty({
    description: 'Array of university codes that are allowed to join this ivent',
    example: ['BOGAZICI', 'ITU'],
  })
  @IsArray()
  @IsString({ each: true, message: 'Each university code must be a string' })
  allowedUniversityCodes!: string[];

  @ApiProperty({
    type: [CollabDto],
    description: 'Array of collaborators for this ivent',
    example: [],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CollabDto)
  collabs!: CollabDto[];

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'URL to Google Forms for registration',
    example: 'https://forms.google.com/...',
    format: 'url',
  })
  @IsUrl({}, { message: 'Google Forms URL must be a valid URL' })
  @IsOptional()
  googleFormsUrl?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Instagram username for the ivent',
    example: 'my_ivent_account',
  })
  @IsString()
  @IsOptional()
  instagramUsername?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'WhatsApp group URL',
    example: 'https://chat.whatsapp.com/...',
    format: 'url',
  })
  @IsUrl({}, { message: 'WhatsApp URL must be a valid URL' })
  @IsOptional()
  whatsappUrl?: string | null;

  @ApiProperty({
    type: 'boolean',
    nullable: true,
    required: false,
    description: 'Whether the WhatsApp URL should be kept private',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isWhatsappUrlPrivate?: boolean | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'WhatsApp contact number',
    example: '+90(555)1234567',
  })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  @IsOptional()
  whatsappNumber?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Phone number for calls',
    example: '+90(555)1234567',
  })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  @IsOptional()
  callNumber?: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Website URL for the ivent',
    example: 'https://myivent.com',
    format: 'url',
  })
  @IsUrl({}, { message: 'Website URL must be a valid URL' })
  @IsOptional()
  websiteUrl?: string | null;
}

export class UpdateDateByIventIdDto {
  @ApiProperty({
    description: 'Array of new date strings in date-time format, in ISO 8601 date-time format',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  @IsArray()
  @IsISO8601({}, { each: true, message: 'Each date must be a valid ISO 8601 date-time string' })
  newDates!: string[];
}

export class UpdateDetailsByIventIdDto {
  @ApiProperty({
    description: 'New description for the ivent',
    example: 'Updated description with new information',
  })
  @IsString()
  newDescription!: string;
}

export class UpdateLocationByIventIdDto {
  @ApiProperty({
    description: 'UUID of the new location',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'Location ID must be a valid UUID v4' })
  newlocationId!: string;
}

export class GetBannerByIventIdDto {
  @ApiProperty({
    format: 'uuid',
    description: 'Array of ivent UUIDs to get banner information for',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each ivent ID must be a valid UUID v4' })
  iventIds!: string[];
}
