import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FriendListingTypeEnum } from 'src/constants/enums';
import { NotificationTypeEnum, UserRelationshipStatusEnum } from 'src/enums';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource, Repository } from 'typeorm';
import { NotificationsService } from '../notifications/notifications.service';
import {
  BlockUserByUserIdParams,
  GetUserBlocklistParams,
  InviteFriendByUserIdParams,
  RemoveFriendByUserIdParams,
  SearchFriendsByUserIdParams,
  UnblockUserByUserIdParams,
} from './models/user-relationships.params';
import { GetUserBlocklistReturn, SearchFriendsByUserIdReturn } from './models/user-relationships.returns';
import { UserFriendships, UserRelationship } from 'src/entities';

@Injectable()
export class UserRelationshipsService {
  constructor(
    private dataSource: DataSource,
    private notificationsService: NotificationsService,
    @InjectRepository(UserRelationship)
    private userRelationshipRepository: Repository<UserRelationship>,
  ) {}

  async getUserBlocklist(getUserBlocklistParams: GetUserBlocklistParams): Promise<GetUserBlocklistReturn> {
    const { sessionId } = getUserBlocklistParams;

    const users = await this.dataSource
      .getRepository(UserRelationship)
      .createQueryBuilder('ur')
      .leftJoinAndSelect('ur.sender', 's')
      .leftJoinAndSelect('ur.receiver', 'r')
      .leftJoinAndSelect('s.university', 'su')
      .leftJoinAndSelect('r.university', 'ru')
      .where('(ur.sender = :sessionId OR ur.receiver = :sessionId)', { sessionId })
      .andWhere('ur.status = :status', { status: UserRelationshipStatusEnum.BLOCKED })
      .getMany();

    return {
      users: users.map((val) => {
        const user = val.sender_id === sessionId ? val.receiver! : val.sender!;
        return {
          userId: user.id,
          username: user.username,
          avatarUrl: user.avatar_url,
          university: user.university?.university_name || null,
        };
      }),
      userCount: users.length,
    };
  }

  async blockUserByUserId(blockUserByUserIdParams: BlockUserByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = blockUserByUserIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't block yourself", HttpStatus.BAD_REQUEST);
    }

    const relationshipResult = await this.dataSource.query(`
      SELECT id
      FROM user_relationships
      WHERE status IN ('accepted', 'pending')
      AND ((sender_id = '${sessionId}' AND receiver_id = '${userId}') OR (sender_id = '${userId}' AND receiver_id = '${sessionId}'));  
    `);

    if (relationshipResult.length) {
      await this.dataSource.createQueryBuilder().update('user_relationships').set({ status: 'blocked' }).where({ id: relationshipResult[0].id }).execute();
    } else {
      await this.dataSource.query(
        insertQueryBuilder({
          tableName: 'user_relationships',
          values: {
            sender_id: sessionId,
            receiver_id: userId,
            status: 'blocked',
          },
        }),
      );
    }

    return {};
  }

  async unblockUserByUserId(unblockUserByUserIdParams: UnblockUserByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = unblockUserByUserIdParams;

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_relationships')
      .where(`status = 'blocked' AND ((sender_id = '${sessionId}' AND receiver_id = '${userId}') OR (sender_id = '${userId}' AND receiver_id = '${sessionId}'))`)
      .execute();

    return {};
  }

  async searchFriendsByUserId(searchFriendsByUserIdParams: SearchFriendsByUserIdParams): Promise<SearchFriendsByUserIdReturn> {
    const { sessionId, userId, type, q, limit, page } = searchFriendsByUserIdParams;

    if (type === FriendListingTypeEnum.GROUP) {
      return {
        groups: [],
        groupCount: 0,
        friends: [],
        friendCount: 0,
      };
    } else {
      const queryBuilder = this.dataSource
        .getRepository(UserFriendships)
        .createQueryBuilder('uf')
        .leftJoinAndSelect('uf.friend', 'f')
        .where('uf.user_id = :userId', { userId })
        .andWhere('uf.status = :status', { status: UserRelationshipStatusEnum.ACCEPTED });

      if (q) {
        queryBuilder.andWhere('(f.username ILIKE :q OR f.firstname ILIKE :q)', { q: `%${q}%` });
      }

      queryBuilder.limit(limit).offset(limit * (page - 1));

      const friends = await queryBuilder.getMany();

      return {
        groups: [],
        groupCount: 0,
        friends: friends.map((friendship: UserFriendships) => {
          const friend = friendship.friend!;
          return {
            userId: friend.id,
            username: friend.username,
            avatarUrl: friend.avatar_url,
            university: friend.university?.university_name || null,
          };
        }),
        friendCount: friends.length,
      };
    }

    //     ? await this.dataSource.query(`
    //   WITH MemberSummariesOfGroups AS (
    //       -- Summaries of their members of the groups
    //       SELECT
    //           CONCAT(ARRAY_TO_STRING((ARRAY_AGG(u.firstname))[:2], ','), '|', COUNT(u.id)) AS member_summary,
    //           gm.group_id AS group_id
    //       FROM group_memberships gm
    //       LEFT JOIN users u ON u.id = gm.member_id
    //       WHERE gm.status IN ('accepted', 'moderator', 'admin')
    //       AND gm.member_id != '${userId}'
    //       GROUP BY gm.group_id
    //   )
    //   SELECT
    //       g.id AS group_id,
    //       g.group_name AS group_name,
    //       msog.member_summary AS member_summary
    //   FROM groups g
    //   LEFT JOIN group_memberships gm ON gm.group_id = g.id
    //   LEFT JOIN MemberSummariesOfGroups msog ON msog.group_id = g.id
    //   WHERE member_id = '${userId}'
    //   AND ${groupQClause}
    //   LIMIT ${limit}
    //   OFFSET ${limit * (page - 1)};
    // `)
  }

  async inviteFriendByUserId(inviteFriendByUserIdParams: InviteFriendByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = inviteFriendByUserIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't add yourself", HttpStatus.BAD_REQUEST);
    }
    const sessionUserResult = await this.dataSource.query(`SELECT username, avatar_url FROM users WHERE id = '${sessionId}'`);

    // const relationshipResult = await this.dataSource.query(`
    //   SELECT id
    //   FROM user_relationships
    //   WHERE status IN ('accepted', 'pending', 'blocked')
    //   AND ((sender_id = '${sessionId}' AND receiver_id = '${userId}') OR (sender_id = '${userId}' AND receiver_id = '${sessionId}'));
    // `);
    // if (relationshipResult.length) {
    //   throw new HttpException('User is already added, invited or blocked', HttpStatus.BAD_REQUEST);
    // }

    // const userRelationshipInsertResult = await this.dataSource.query(
    //   insertQueryBuilder({
    //     tableName: 'user_relationships',
    //     values: {
    //       sender_id: sessionId,
    //       receiver_id: userId,
    //       status: 'pending',
    //     },
    //   }),
    // );

    const userRelationshipInsertResult = await this.userRelationshipRepository
      .createQueryBuilder()
      .insert()
      .into(UserRelationship)
      .values({
        sender_id: sessionId,
        receiver_id: userId,
        status: UserRelationshipStatusEnum.PENDING,
      })
      .execute();

    // Create notification
    await this.notificationsService.sendNotifications(
      {
        notification_type: NotificationTypeEnum.ARKADASLIK_ISTEGI_GONDERDI,
        account_type: 'user',
        account_id: sessionId,
        account_name: sessionUserResult[0].username,
        account_image_url: sessionUserResult[0].avatar_url,
        subject_id: userRelationshipInsertResult.raw[0].id,
      },
      [userId],
    );

    return {};
  }

  async removeFriendByUserId(removeFriendByUserIdParams: RemoveFriendByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, userId } = removeFriendByUserIdParams;

    await this.userRelationshipRepository
      .createQueryBuilder()
      .delete()
      .from(UserRelationship)
      .where('status IN (:...statuses)', {
        statuses: [UserRelationshipStatusEnum.ACCEPTED, UserRelationshipStatusEnum.PENDING],
      })
      .andWhere('(sender_id = :senderId AND receiver_id = :receiverId) OR (sender_id = :receiverId AND receiver_id = :senderId)', {
        senderId: sessionId,
        receiverId: userId,
      })
      .execute();

    return {};
  }
}
