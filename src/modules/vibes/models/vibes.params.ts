import { VibePrivacyEnum } from 'src/enums';

export type CreateVibeParams = {
  sessionId: string;
  file: Express.Multer.File;
  caption?: string | null;
  vibeFolderId: string;
  privacy: VibePrivacyEnum;
};

export type DeleteVibeByVibeIdParams = {
  sessionId: string;
  vibeId: string;
};

export type GetCommentsByVibeIdParams = {
  sessionId: string;
  vibeId: string;
  limit: number;
  page: number;
};

export type GetLikesByVibeIdParams = {
  sessionId: string;
  vibeId: string;
};

export type GetVibeByVibeIdParams = {
  sessionId: string;
  vibeId: string;
};

export type GetVibesParams = {
  sessionId: string;
  limit: number;
  page: number;
};

export type HideByVibeIdParams = {
  sessionId: string;
  vibeId: string;
};

export type LikeByVibeIdParams = {
  sessionId: string;
  vibeId: string;
};

export type ShowByVibeIdParams = {
  sessionId: string;
  vibeId: string;
};

export type UnlikeByVibeIdParams = {
  sessionId: string;
  vibeId: string;
};

export type UpdateByVibeIdParams = {
  sessionId: string;
  vibeId: string;
  newCaption: string;
};
