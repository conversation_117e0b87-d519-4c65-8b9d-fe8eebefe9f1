import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';
import { VibePrivacyEnum } from 'src/enums';

export class CreateVibeDto {
  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Caption or description for the vibe',
    example: 'Amazing sunset at the beach! 🌅',
    maxLength: 500,
  })
  @IsString()
  @MaxLength(500, { message: 'Caption must be 500 characters or less' })
  @IsOptional()
  caption?: string | null;

  @ApiProperty({
    description: 'UUID of the folder this vibe belongs to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID('4', { message: 'Vibe folder ID must be a valid UUID v4' })
  vibeFolderId!: string;

  @ApiProperty({
    enum: VibePrivacyEnum,
    enumName: 'VibePrivacyEnum',
    description: 'Privacy setting for the vibe',
    example: VibePrivacyEnum.PUBLIC,
  })
  @IsEnum(VibePrivacyEnum, { message: 'Privacy must be a valid VibePrivacyEnum value' })
  privacy!: VibePrivacyEnum;
}

export class UpdateByVibeIdDto {
  @ApiProperty({
    description: 'New caption for the vibe',
    example: 'Updated caption with new information',
    maxLength: 500,
  })
  @IsString()
  @MaxLength(500, { message: 'Caption must be 500 characters or less' })
  newCaption!: string;
}
