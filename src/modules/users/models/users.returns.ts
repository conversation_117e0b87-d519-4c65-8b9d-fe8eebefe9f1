import { ApiProperty } from '@nestjs/swagger';
import { UserRelationshipStatusEnum, UserRoleEnum } from 'src/enums';
import {
  IventListItem,
  SideMenuPageItem,
  UserListItem,
  UserListItemWithPhoneNumber,
  UserListItemWithRelationshipStatus,
} from 'src/models';
import { MemoryFolderCardItem } from 'src/models/memory-folder-card-item';
import { VibeFolderCardItem } from 'src/models/vibe-folder-card-item';

export class GetContactsByUserIdReturn {
  @ApiProperty({
    type: [UserListItemWithPhoneNumber],
    description: 'List of user contacts with their phone numbers',
    example: [],
  })
  contacts!: UserListItemWithPhoneNumber[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of contacts found',
    example: 0,
    minimum: 0,
  })
  contactCount!: number;
}

export class GetFavoritesByUserIdReturn {
  @ApiProperty({
    type: [IventListItem],
    description: "List of user's favorite ivents",
    example: [],
  })
  ivents!: IventListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of favorite ivents',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class GetFollowerFriendsByUserIdReturn {
  @ApiProperty({
    type: [UserListItem],
    description: 'List of friends who are also followers',
    example: [],
  })
  friends!: UserListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of follower friends',
    example: 0,
    minimum: 0,
  })
  friendCount!: number;
}

export class GetFollowersByUserIdReturn {
  @ApiProperty({
    description: 'List of friend usernames',
    example: [],
  })
  friendUsernames!: string[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of friends',
    example: 2,
    minimum: 0,
  })
  friendCount!: number;

  @ApiProperty({
    type: [UserListItemWithRelationshipStatus],
    description: 'List of followers with their relationship status',
    example: [],
  })
  followers!: UserListItemWithRelationshipStatus[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of followers',
    example: 0,
    minimum: 0,
  })
  followerCount!: number;

  @ApiProperty({
    description: "Whether this is the current user's own profile",
    example: false,
  })
  isFirstPerson!: boolean;
}

export class GetFollowingsByUserIdReturn {
  @ApiProperty({
    type: [UserListItemWithRelationshipStatus],
    description: 'List of users being followed',
    example: [],
  })
  followings!: UserListItemWithRelationshipStatus[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of users being followed',
    example: 0,
    minimum: 0,
  })
  followingCount!: number;
}

export class GetIventsByUserIdReturn {
  @ApiProperty({
    type: [IventListItem],
    description: 'List of ivents created by the user',
    example: [],
  })
  ivents!: IventListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of ivents created by the user',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;

  @ApiProperty({
    description: "Whether this is the current user's own profile",
    example: false,
  })
  isFirstPerson!: boolean;
}

export class GetLevelByUserIdReturn {
  @ApiProperty({
    enum: UserRoleEnum,
    enumName: 'UserRoleEnum',
    description: 'User level (role) information',
    example: UserRoleEnum.LEVEL_0,
  })
  levelInfo!: UserRoleEnum;
}

export class GetPagesByUserIdReturn {
  @ApiProperty({
    type: [SideMenuPageItem],
    description: 'List of pages the user is associated with',
    example: [],
  })
  pages!: SideMenuPageItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of pages',
    example: 0,
    minimum: 0,
  })
  pageCount!: number;
}

export class GetMemoryFoldersByUserIdReturn {
  @ApiProperty({
    type: [MemoryFolderCardItem],
    description: "List of user's memory folders",
    example: [],
  })
  memoryFolders!: MemoryFolderCardItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of memory folders',
    example: 0,
    minimum: 0,
  })
  memoryFolderCount!: number;
}

export class GetUserByUserIdReturn {
  @ApiProperty({
    description: 'Unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId!: string;

  @ApiProperty({
    enum: UserRoleEnum,
    enumName: 'UserRoleEnum',
    description: 'Role of the user',
    example: UserRoleEnum.LEVEL_0,
  })
  userRole!: UserRoleEnum;

  @ApiProperty({
    description: 'Username of the user',
    example: 'john_doe',
  })
  username!: string;

  @ApiProperty({
    description: 'Full name of the user',
    example: 'John Doe',
  })
  fullname!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  avatarUrl?: string | null;

  @ApiProperty({
    type: 'integer',
    description: 'Number of ivents created by the user',
    example: 5,
    minimum: 0,
  })
  iventCount!: number;

  @ApiProperty({
    type: 'integer',
    description: 'Number of friends the user has',
    example: 10,
    minimum: 0,
  })
  friendCount!: number;

  @ApiProperty({
    type: 'integer',
    description: 'Number of followers the user has',
    example: 15,
    minimum: 0,
  })
  followerCount!: number;

  @ApiProperty({
    type: 'integer',
    description: 'Number of users the user is following',
    example: 15,
    minimum: 0,
  })
  followingCount!: number;

  @ApiProperty({
    description: "List of user's hobbies",
    example: ['Photography', 'Travel'],
  })
  hobbies!: string[];

  @ApiProperty({
    description: 'Whether the current user is following this user',
    example: false,
  })
  isFollowing!: boolean;

  @ApiProperty({
    description: "Whether this is the current user's own profile",
    example: false,
  })
  isFirstPerson!: boolean;

  @ApiProperty({
    enum: UserRelationshipStatusEnum,
    nullable: true,
    required: false,
    enumName: 'UserRelationshipStatusEnum',
    description: 'Status of the relationship between the current user and the user in the list',
    example: UserRelationshipStatusEnum.ACCEPTED,
  })
  relationshipStatus?: UserRelationshipStatusEnum | null;
}

export class GetVibeFoldersByUserIdReturn {
  @ApiProperty({
    type: [VibeFolderCardItem],
    description: "List of user's vibe folders",
    example: [],
  })
  vibeFolders!: VibeFolderCardItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of vibe folders',
    example: 0,
    minimum: 0,
  })
  vibeFolderCount!: number;
}

export class RegisterReturn {
  @ApiProperty({
    description: 'Unique identifier of the newly registered user',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId!: string;

  @ApiProperty({
    description: 'JWT authentication token for the new user',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token!: string;

  @ApiProperty({
    enum: UserRoleEnum,
    enumName: 'UserRoleEnum',
    description: 'Role of the new user',
    example: UserRoleEnum.LEVEL_0,
  })
  role!: UserRoleEnum;

  @ApiProperty({
    description: 'Username of the new user',
    example: 'john_doe',
  })
  username!: string;

  @ApiProperty({
    description: 'Full name of the new user',
    example: 'John Doe',
  })
  fullname!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  avatarUrl?: string | null;
}

export class GetUserBannerByUserIdReturn {
  @ApiProperty({
    description: 'Unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId!: string;

  @ApiProperty({
    description: 'Username of the user',
    example: 'john_doe',
  })
  username!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  avatarUrl?: string | null;

  @ApiProperty({
    description: 'Full name of the user',
    example: 'John Doe',
  })
  fullname!: string;
}
