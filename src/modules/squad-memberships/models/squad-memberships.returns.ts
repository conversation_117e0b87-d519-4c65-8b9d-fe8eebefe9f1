import { ApiProperty } from '@nestjs/swagger';
import { IventViewTypeEnum } from 'src/enums';
import { GroupListItem, UserListItem, UserListItemWithRelationshipStatus } from 'src/models';

export class SearchInvitableUsersByIventIdReturn {
  @ApiProperty({
    type: [GroupListItem],
    description: 'List of groups that can be invited to the ivent',
    example: [],
  })
  groups!: GroupListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of invitable groups',
    example: 0,
    minimum: 0,
  })
  groupCount!: number;

  @ApiProperty({
    type: [UserListItem],
    description: 'List of users that can be invited to the ivent',
    example: [],
  })
  friends!: UserListItem[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of invitable users',
    example: 0,
    minimum: 0,
  })
  friendCount!: number;
}

export class SearchParticipantsByIventIdReturn {
  @ApiProperty({
    type: [UserListItemWithRelationshipStatus],
    description: 'List of users with their relationship status',
    example: [],
  })
  users!: UserListItemWithRelationshipStatus[];

  @ApiProperty({
    type: 'integer',
    description: 'Total number of users',
    example: 0,
    minimum: 0,
  })
  userCount!: number;

  @ApiProperty({
    enum: IventViewTypeEnum,
    enumName: 'IventViewTypeEnum',
    description: 'View type of the ivent for the current user',
    example: IventViewTypeEnum.DEFAULT,
  })
  viewType!: IventViewTypeEnum;
}
