import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString, IsUUID } from 'class-validator';
import { MediaFormatEnum } from 'src/enums';

export class CreateMemoryDto {
  @ApiProperty({
    enum: MediaFormatEnum,
    enumName: 'MediaFormatEnum',
  })
  @IsEnum(MediaFormatEnum)
  mediaFormat!: MediaFormatEnum;

  @ApiProperty({
    type: 'string',
    nullable: true,
    required: false,
    description: 'Caption or description for the memory',
    example: 'Amazing sunset at the beach! 🌅',
    maxLength: 500,
  })
  @IsString()
  caption?: string | null;

  @ApiProperty()
  @IsUUID()
  squadId!: string;
}
