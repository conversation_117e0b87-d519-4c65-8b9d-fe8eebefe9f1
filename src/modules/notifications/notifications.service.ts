import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import { GetNotificationsParams, ReplyInvitationByNotificationIdParams } from './models/notifications.params';
import { GetNotificationsReturn } from './models/notifications.returns';
import { NotificationTypeEnum } from 'src/enums';

@Injectable()
export class NotificationsService {
  constructor(private dataSource: DataSource) {}

  async getNotifications(getNotificationsParams: GetNotificationsParams): Promise<GetNotificationsReturn> {
    const { sessionId, limit, page } = getNotificationsParams;

    const notificationsResult = await this.dataSource.query(`
      SELECT
        n.notification_type AS notification_type,
        n.id AS notification_id,
        n.created_at AS created_at,
        n.account_type AS account_type,
        n.account_id AS account_id,
        n.account_name AS account_name,
        n.account_image_url AS account_image_url,
        n.content_type AS content_type,
        n.content_id AS content_id,
        n.content_thumbnail_url AS content_thumbnail_url,
        n.content_name AS content_name,
        n.content_item AS content_item,
        n.action_type AS action_type,
        n.action_id AS action_id
      FROM user_notifications un
        LEFT JOIN notifications n ON n.id = un.notification_id 
      WHERE un.user_id = '${sessionId}'
        AND n.status = 'active'
      ORDER BY n.created_at DESC
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      notifications: notificationsResult.map((val) => ({
        notificationType: val.notification_type,
        notificationId: val.notification_id,
        createdAt: val.created_at,
        accountType: val.account_type,
        accountId: val.account_id,
        accountName: val.account_name,
        accountImageUrl: val.account_image_url,
        contentType: val.content_type,
        contentId: val.content_id,
        contentThumbnailUrl: val.content_thumbnail_url,
        contentName: val.content_name,
        contentItem: val.content_item,
        actionType: val.action_type,
        actionId: val.action_id,
      })),
      notificationCount: notificationsResult.length,
    };
  }

  async replyInvitationByNotificationId(
    replyInvitationByNotificationIdParams: ReplyInvitationByNotificationIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, notificationId, replyType } = replyInvitationByNotificationIdParams;

    const sessionUserResult = await this.dataSource.query(
      `SELECT username, avatar_url FROM users WHERE id = '${sessionId}'`,
    );

    const notificationResult = await this.dataSource.query(`
      SELECT *
      FROM notifications
      WHERE id = '${notificationId}'
      AND status = 'active';
    `);
    if (notificationResult.length === 0) {
      throw new HttpException('BAD_REQUEST', HttpStatus.BAD_REQUEST);
    }
    const notificationType = notificationResult[0].notification_type;

    // Check if the notification type is valid
    // if (!InvitationNotifications.includes(notificationType)) {
    //   throw new HttpException('Notification type is not valid', HttpStatus.BAD_REQUEST);
    // }

    if (notificationType === NotificationTypeEnum.ARKADASLIK_ISTEGI_GONDERDI) {
      await this.dataSource
        .createQueryBuilder()
        .update('user_relationships')
        .set({
          status: replyType === 'accept' ? 'accepted' : 'rejected',
        })
        .where({
          id: notificationResult[0].subject_id,
        })
        .execute();

      if (replyType === 'accept') {
        // Create after-notification for the receiver
        await this.sendNotifications(
          {
            notification_type: NotificationTypeEnum.ARTIK_ARKADASSINIZ,
            account_type: 'user',
            account_id: notificationResult[0].account_id,
            account_name: notificationResult[0].account_name,
            account_image_url: notificationResult[0].account_image_url,
          },
          [sessionId],
        );

        // Create after-notification for the sender
        await this.sendNotifications(
          {
            notification_type: NotificationTypeEnum.ARKADASLIK_ISTEGI_ONAYLADI,
            account_type: 'user',
            account_id: sessionId,
            account_name: sessionUserResult[0].username,
            account_image_url: sessionUserResult[0].avatar_url,
          },
          [notificationResult[0].account_id],
        );
      }
    } else if (notificationType === NotificationTypeEnum.UYELIK_ISTEGI_GONDERDI) {
    } else if (notificationType === NotificationTypeEnum.UYELIK_DAVETI) {
    } else if (notificationType === NotificationTypeEnum.IVENTE_DAVET_ETTI) {
      await this.dataSource
        .createQueryBuilder()
        .update('squad_memberships')
        .set({
          status: replyType === 'accept' ? 'accepted' : 'rejected',
        })
        .where({
          id: notificationResult[0].subject_id,
        })
        .execute();

      if (replyType === 'accept') {
        // Create after-notification for the sender
        await this.sendNotifications(
          {
            notification_type: 'type_13',
            account_type: 'user',
            account_id: sessionId,
            account_name: sessionUserResult[0].username,
            account_image_url: sessionUserResult[0].avatar_url,
            content_type: 'ivent',
            content_id: notificationResult[0].content_id,
            content_name: notificationResult[0].content_name,
            content_thumbnail_url: notificationResult[0].content_thumbnail_url,
          },
          [notificationResult[0].account_id],
        );
      }
    } else if (notificationType === NotificationTypeEnum.WHATSAPP_GRUBUNA_KATILMA_ISTEGI_GONDERDI) {
    } else if (notificationType === NotificationTypeEnum.IVENTE_PAYDAS_OLARAK_EKLEME_ISTEGI) {
    }

    await this.dataSource
      .createQueryBuilder()
      .update('notifications')
      .set({
        status: 'replied',
      })
      .where({
        id: notificationId,
      })
      .execute();

    return {};
  }

  async sendNotifications(notificationBody: Record<string, any>, userIds: string[]): Promise<Record<string, never>> {
    // Check if there are any available receiver for the notification
    if (userIds.length) {
      // Create the notification template
      const notificationInsertResult = await this.dataSource.query(
        insertQueryBuilder({
          tableName: 'notifications',
          values: notificationBody,
        }),
      );
      const notificationId = notificationInsertResult[0].id;

      // Insert notifications for the users that are invited to the squad
      await this.dataSource.query(
        insertQueryBuilder({
          tableName: 'user_notifications',
          values: userIds.map((val) => ({
            notification_id: notificationId,
            user_id: val,
          })),
        }),
      );
    }

    return {};
  }
}
