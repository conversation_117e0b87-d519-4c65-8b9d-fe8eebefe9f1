import { MigrationInterface, QueryRunner } from "typeorm";

export class EnumSchema1758575069476 implements MigrationInterface {
    name = 'EnumSchema1758575069476'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."ivent_user_status_enum" AS ENUM('admin', 'pending', 'accepted', 'joined')`);
        await queryRunner.query(`CREATE TYPE "public"."ivent_user_type_enum" AS ENUM('page', 'user', 'member')`);
        await queryRunner.query(`CREATE TYPE "public"."ivent_view_type_enum" AS ENUM('joined', 'created', 'default')`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."ivent_user_status_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."ivent_user_type_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."ivent_view_type_enum"`);
    }
}