import { MigrationInterface, QueryRunner } from "typeorm";

export class InitSchema1758575069747 implements MigrationInterface {
    name = 'InitSchema1758575069747'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "auth" ("phone_number" character varying NOT NULL, "code" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_013ebd35a4410a1b79111495f97" PRIMARY KEY ("phone_number"))`);
        await queryRunner.query(`CREATE TABLE "distributors" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "distributor_name" character varying NOT NULL, "thumbnail_url" character varying NOT NULL, "website_url" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_a3741291eb0af96f795b25d90b4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ivent_tags" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "ivent_id" uuid NOT NULL, "hobby_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_6f9390649a1908ee141c3015223" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."memories_media_format_enum" AS ENUM('image', 'video')`);
        await queryRunner.query(`CREATE TYPE "public"."memories_creator_type_enum" AS ENUM('page', 'user')`);
        await queryRunner.query(`CREATE TABLE "memories" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "media_format" "public"."memories_media_format_enum" NOT NULL, "thumbnail_url" text, "creator_type" "public"."memories_creator_type_enum" NOT NULL, "caption" text, "creator_user_id" uuid, "creator_page_id" uuid, "memory_folder_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_aaa0692d9496fe827b0568612f8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "memory_folders" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "thumbnail_url" text, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_116e7ce8357625f6b369e5fd838" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "vibe_likes" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "vibe_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_4f5cbee5e044351c70a5647d733" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "vibe_views" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "vibe_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_d1e546be5e34d1d987063278b94" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."vibes_media_format_enum" AS ENUM('image', 'video')`);
        await queryRunner.query(`CREATE TYPE "public"."vibes_creator_type_enum" AS ENUM('page', 'user')`);
        await queryRunner.query(`CREATE TYPE "public"."vibes_privacy_enum" AS ENUM('private', 'friends', 'friends_of_friends', 'public')`);
        await queryRunner.query(`CREATE TABLE "vibes" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "media_format" "public"."vibes_media_format_enum" NOT NULL, "thumbnail_url" text, "creator_type" "public"."vibes_creator_type_enum" NOT NULL, "caption" text, "is_visible" boolean NOT NULL DEFAULT true, "privacy" "public"."vibes_privacy_enum" NOT NULL DEFAULT 'public', "creator_user_id" uuid, "creator_page_id" uuid, "vibe_folder_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_9fdc48d64f47ce1fc6be51078fd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "vibe_folders" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "thumbnail_url" text, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_0df1b43a272148468b3988a1892" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "squads" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "creator_id" uuid NOT NULL, "ivent_id" uuid NOT NULL, "vibe_folder_id" uuid NOT NULL, "memory_folder_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "REL_50556d38ebc8c8688dcb54c349" UNIQUE ("vibe_folder_id"), CONSTRAINT "REL_33902dbbe9294dba4df78acbaf" UNIQUE ("memory_folder_id"), CONSTRAINT "PK_6ef0717a3dbb0f326bc387dfacb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."squad_memberships_status_enum" AS ENUM('pending', 'joined', 'accepted')`);
        await queryRunner.query(`CREATE TABLE "squad_memberships" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."squad_memberships_status_enum" NOT NULL, "member_id" uuid NOT NULL, "squad_id" uuid NOT NULL, "inviter_id" uuid, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_4ec653d2980a35653645e9cafda" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."ivent_collabs_status_enum" AS ENUM('admin', 'pending', 'accepted')`);
        await queryRunner.query(`CREATE TYPE "public"."ivent_collabs_collab_type_enum" AS ENUM('page', 'user')`);
        await queryRunner.query(`CREATE TABLE "ivent_collabs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."ivent_collabs_status_enum" NOT NULL, "collab_type" "public"."ivent_collabs_collab_type_enum" NOT NULL, "collab_user_id" uuid, "collab_page_id" uuid, "ivent_id" uuid NOT NULL, "inviter_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_8bb625effea073bf2f54e841b4c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ivent_universities" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "ivent_id" uuid NOT NULL, "university_code" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_eb2dd1ffa2c4bd5b1fa7adebab4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "universities" ("university_code" character varying NOT NULL, "university_name" character varying(255) NOT NULL, "image_url" text, "location_id" uuid, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_dd3afbbe0e03ca515325ec68c72" PRIMARY KEY ("university_code"))`);
        await queryRunner.query(`CREATE TABLE "locations" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "location_name" character varying(255) NOT NULL, "open_address" text NOT NULL, "district" character varying(255), "city" character varying, "mapbox_id" character varying(255), "geom" geography(Point,4326) NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_7cc1c9e3853b94816c094825e74" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "page_blacklists" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "blocked_id" uuid NOT NULL, "page_id" uuid NOT NULL, "blocker_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_1b364ca8347f52b1b9042fe49ff" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "page_blocks_by_user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "page_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_34cd2e70869210a67743273a4cb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "page_followers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "page_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_b5ad5c34d436fa82b0a61c8b9a2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."page_memberships_status_enum" AS ENUM('admin', 'moderator', 'creator', 'pending', 'accepted', 'blocked')`);
        await queryRunner.query(`CREATE TABLE "page_memberships" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."page_memberships_status_enum" NOT NULL, "member_id" uuid NOT NULL, "page_id" uuid NOT NULL, "inviter_id" uuid, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_44b5852141eaf0f66481e0d2986" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "page_subscribers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "page_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_6d9812d28508163061a554d0fe5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."notifications_status_enum" AS ENUM('active', 'replied')`);
        await queryRunner.query(`CREATE TYPE "public"."notifications_notification_type_enum" AS ENUM('arkadaslik_istegi_gonderdi', 'artik_arkadassiniz', 'arkadaslik_istegi_onayladi', 'uyelik_istegi_gonderdi', 'uyelik_istegi_kabul_etti', 'sayfani_takip_etti', 'iventine_vibe_ekledi', 'sayfana_ivent_ekledi', 'uyelik_daveti', 'sayfana_uye_oldu', 'vibeina_medya_ekledi', 'ivente_davet_etti', 'ivent_davetini_kabul_etti', 'vibeini_begendi', 'uygulamaya_katildi', 'ivent_bilgilerini_guncelledi', 'ivent_iptal', 'whatsapp_grubuna_katilma_istegi_gonderdi', 'whatsapp_grubuna_katilma_istegi_onaylandi', 'ivente_paydas_olarak_ekleme_istegi', 'ivente_paydaslik_istegi_kabul_edildi', 'iventini_x_kisi_favoriledi', 'iventine_katildi', 'ivent_yayinladi', 'ivente_x_adet_memory_ekledi', 'arkadas_grubuna_ekledi', 'vibe_eklemek_icin_sonuc_saat', 'vibe_eklemek_icin_yirmi_dort_saat', 'ivent_yaklasiyor', 'ivent_creator_basvurunuz_onaylandi', 'ivent_creator_basvurunuz_reddedildi', 'sayfa_basvurunuz_onaylandi', 'sayfa_basvurunuz_reddedildi', 'vibe_yorum_yapti', 'sayfaya_uye_oldunuz', 'vibeiniza_eklediginiz_medyayi_gizlediniz', 'vibeiniza_eklediginiz_medyayi_eklediniz', 'whatsapp_grubuna_katilma_istegi_onayladiniz', 'ivente_paydas_oldunuz', 'ile_ivente_katildiniz', 'ivente_katiliyor')`);
        await queryRunner.query(`CREATE TABLE "notifications" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."notifications_status_enum" NOT NULL DEFAULT 'active', "account_type" character varying NOT NULL, "account_id" uuid NOT NULL, "account_image_url" text, "account_name" character varying, "content_type" character varying, "content_id" uuid, "content_thumbnail_url" text, "content_name" character varying, "content_item" character varying, "action_type" character varying, "action_id" uuid, "notification_type" "public"."notifications_notification_type_enum" NOT NULL, "subject_id" uuid, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_6a72c3c0f683f6462415e653c3a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_notifications" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "notification_id" uuid NOT NULL, "page_id" uuid, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_569622b0fd6e6ab3661de985a2b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "pages" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "page_name" character varying(255) NOT NULL, "thumbnail_url" text, "website_url" text, "description" text, "is_edu" boolean NOT NULL DEFAULT false, "have_membership" boolean NOT NULL DEFAULT false, "location_id" uuid, "creator_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_8f21ed625aa34c8391d636b7d3b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "page_tags" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "page_id" uuid NOT NULL, "hobby_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_b56dc8b2ed77c8f9f164fc44831" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_hobbies" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "hobby_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_9f4b5afa1d2484defd6f24d1032" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "hobbies" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "hobby_name" character varying NOT NULL, "level" integer NOT NULL, "parent_hobby_id" uuid, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_4dce5d186533856c1f47e2a76d7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ivent_dates" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "ivent_id" uuid NOT NULL, "ivent_date" TIMESTAMP WITH TIME ZONE NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_0bba6a9fa976776ef2e97e3be86" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_favorites" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "favorited_ivent_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_6c472a19a7423cfbbf6b7c75939" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."ivents_creator_type_enum" AS ENUM('user', 'page', 'distributor')`);
        await queryRunner.query(`CREATE TYPE "public"."ivents_privacy_enum" AS ENUM('private', 'friends', 'edu', 'selected_edu', 'public')`);
        await queryRunner.query(`CREATE TABLE "ivents" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "ivent_name" character varying(255) NOT NULL, "thumbnail_url" text, "creator_type" "public"."ivents_creator_type_enum" NOT NULL, "description" text, "privacy" "public"."ivents_privacy_enum" NOT NULL DEFAULT 'public', "google_forms_url" text, "instagram_username" character varying(255), "whatsapp_url" text, "is_whatsapp_url_private" boolean NOT NULL DEFAULT false, "whatsapp_number" character varying(255), "call_number" character varying(255), "website_url" text, "creator_user_id" uuid, "creator_page_id" uuid, "creator_distributor_id" uuid, "location_id" uuid NOT NULL, "vibe_folder_id" uuid NOT NULL, "memory_folder_id" uuid NOT NULL, "category_tag_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "REL_e17ed8bbbbd2fba5d182c8b2fc" UNIQUE ("vibe_folder_id"), CONSTRAINT "REL_7700168dc08d3a2b8442e0128d" UNIQUE ("memory_folder_id"), CONSTRAINT "PK_072316142c06ef8329008a613d9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ivent_groups" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "ivent_id" uuid NOT NULL, "group_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_2ac6896de4f73ba17a21f3636a7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "groups" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "group_name" character varying(255) NOT NULL, "thumbnail_url" text, "creator_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_659d1483316afb28afd3a90646e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."group_memberships_status_enum" AS ENUM('admin', 'moderator', 'pending', 'accepted')`);
        await queryRunner.query(`CREATE TABLE "group_memberships" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."group_memberships_status_enum" NOT NULL, "member_id" uuid NOT NULL, "group_id" uuid NOT NULL, "inviter_id" uuid, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_4a04ebe9f25ad41f45b2c0ca4b5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_contacts" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "phone_number" character varying(255) NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_c7048d25b5fda1fa70501fac9ca" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."user_followers_status_enum" AS ENUM('pending', 'accepted')`);
        await queryRunner.query(`CREATE TABLE "user_followers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."user_followers_status_enum" NOT NULL, "following_id" uuid NOT NULL, "follower_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_ee6ca6c8db6c5e06db7727f08d8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."user_relationships_status_enum" AS ENUM('pending', 'accepted', 'blocked')`);
        await queryRunner.query(`CREATE TABLE "user_relationships" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."user_relationships_status_enum" NOT NULL DEFAULT 'pending', "sender_id" uuid NOT NULL, "receiver_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_a9f4f64c43f6ec154dd602c47d8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_subscribers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "subscriber_id" uuid NOT NULL, "user_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_0b938afc2650e4cd97e945d080d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."users_role_enum" AS ENUM('level_0', 'level_1', 'level_2', 'level_3', 'level_4', 'level_5', 'level_6', 'creator')`);
        await queryRunner.query(`CREATE TYPE "public"."users_edu_verification_enum" AS ENUM('unverified', 'student', 'grad')`);
        await queryRunner.query(`CREATE TYPE "public"."users_gender_enum" AS ENUM('male', 'female', 'non-binary', 'other', 'prefer_not_to_say')`);
        await queryRunner.query(`CREATE TYPE "public"."users_ivent_privacy_enum" AS ENUM('private', 'friends', 'public')`);
        await queryRunner.query(`CREATE TYPE "public"."users_friends_privacy_enum" AS ENUM('friends', 'public')`);
        await queryRunner.query(`CREATE TABLE "users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "role" "public"."users_role_enum" NOT NULL DEFAULT 'level_0', "edu_verification" "public"."users_edu_verification_enum" NOT NULL DEFAULT 'unverified', "email" character varying(255), "username" character varying(255) NOT NULL, "phone_number" character varying(255) NOT NULL, "firstname" character varying(255) NOT NULL, "lastname" character varying(255), "gender" "public"."users_gender_enum", "birthday" character varying(255), "email_verification" character varying(6), "email_verification_expires_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "avatar_url" text, "is_push_notifications_on" boolean NOT NULL DEFAULT true, "is_email_notifications_on" boolean NOT NULL DEFAULT true, "ivent_privacy" "public"."users_ivent_privacy_enum" NOT NULL DEFAULT 'friends', "friends_privacy" "public"."users_friends_privacy_enum" NOT NULL DEFAULT 'friends', "last_login" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "university_code" character varying, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "comments" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "comment" text NOT NULL, "vibe_id" uuid NOT NULL, "creator_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_8bf68bc960f2b69e818bdb90dcb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "ivent_tags" ADD CONSTRAINT "FK_e306d6f64d1638ab5d7394bfac8" FOREIGN KEY ("ivent_id") REFERENCES "ivents"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_tags" ADD CONSTRAINT "FK_db2faba6b45158bf84482178478" FOREIGN KEY ("hobby_id") REFERENCES "hobbies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "memories" ADD CONSTRAINT "FK_34c0e97a21d2602c2fed3fc6af3" FOREIGN KEY ("creator_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "memories" ADD CONSTRAINT "FK_f78a58bd5fadaabd7148f27789d" FOREIGN KEY ("creator_page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "memories" ADD CONSTRAINT "FK_963291cacc03e98f36343f866e7" FOREIGN KEY ("memory_folder_id") REFERENCES "memory_folders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vibe_likes" ADD CONSTRAINT "FK_1c7a559f665b286b896670104ed" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vibe_likes" ADD CONSTRAINT "FK_ce2e92af19464c8da031296c8a2" FOREIGN KEY ("vibe_id") REFERENCES "vibes"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vibe_views" ADD CONSTRAINT "FK_8d2363265646fe33c79e05789ec" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vibe_views" ADD CONSTRAINT "FK_ec34289ebe49b3b1d5aab1c53f1" FOREIGN KEY ("vibe_id") REFERENCES "vibes"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vibes" ADD CONSTRAINT "FK_e3d8164f525270eb60ffcedcd96" FOREIGN KEY ("creator_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vibes" ADD CONSTRAINT "FK_1c65f10879a29fa23da49b7c2ee" FOREIGN KEY ("creator_page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vibes" ADD CONSTRAINT "FK_39bc36724aedec7b61f0d415d88" FOREIGN KEY ("vibe_folder_id") REFERENCES "vibe_folders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "squads" ADD CONSTRAINT "FK_695ffc086f02e03f621415c1985" FOREIGN KEY ("creator_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "squads" ADD CONSTRAINT "FK_f039b8c83ea780372366b8d9857" FOREIGN KEY ("ivent_id") REFERENCES "ivents"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "squads" ADD CONSTRAINT "FK_50556d38ebc8c8688dcb54c3495" FOREIGN KEY ("vibe_folder_id") REFERENCES "vibe_folders"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "squads" ADD CONSTRAINT "FK_33902dbbe9294dba4df78acbaf6" FOREIGN KEY ("memory_folder_id") REFERENCES "memory_folders"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "squad_memberships" ADD CONSTRAINT "FK_089814640020507508563ec412d" FOREIGN KEY ("member_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "squad_memberships" ADD CONSTRAINT "FK_63480c6e49a2d97ce95370654bc" FOREIGN KEY ("squad_id") REFERENCES "squads"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "squad_memberships" ADD CONSTRAINT "FK_7b9c958ac5efa4631190b644f3e" FOREIGN KEY ("inviter_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_collabs" ADD CONSTRAINT "FK_a5da1d0158e5088d3f9e7c78855" FOREIGN KEY ("collab_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_collabs" ADD CONSTRAINT "FK_a035776161490da96990b4aac92" FOREIGN KEY ("collab_page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_collabs" ADD CONSTRAINT "FK_c5f1cb13a61d21aad3a20758b2e" FOREIGN KEY ("ivent_id") REFERENCES "ivents"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_collabs" ADD CONSTRAINT "FK_fc60b10934fa5b533b012ca76c0" FOREIGN KEY ("inviter_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_universities" ADD CONSTRAINT "FK_ed20249c7a3e0cf4b7bc1a1e808" FOREIGN KEY ("ivent_id") REFERENCES "ivents"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_universities" ADD CONSTRAINT "FK_2d3e8cd913716c7563ca643d9d9" FOREIGN KEY ("university_code") REFERENCES "universities"("university_code") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "universities" ADD CONSTRAINT "FK_2ca32288ef68bfc2d1e1dd3f304" FOREIGN KEY ("location_id") REFERENCES "locations"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_blacklists" ADD CONSTRAINT "FK_7aaf629d46723b7346d6701577b" FOREIGN KEY ("blocked_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_blacklists" ADD CONSTRAINT "FK_90249012c8e5feb289f1bb6672e" FOREIGN KEY ("page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_blacklists" ADD CONSTRAINT "FK_153fab56c1c0b8b80a23c1590a7" FOREIGN KEY ("blocker_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_blocks_by_user" ADD CONSTRAINT "FK_4cb6bada343671ad567f7faaa9d" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_blocks_by_user" ADD CONSTRAINT "FK_b99e8f60ef3779041e78d892711" FOREIGN KEY ("page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_followers" ADD CONSTRAINT "FK_53d741cc7ddb64911edb7aaa218" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_followers" ADD CONSTRAINT "FK_7301faa62f4cfbdd9e931e715e1" FOREIGN KEY ("page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_memberships" ADD CONSTRAINT "FK_3f5bb0581d77f2d1a4767148cc4" FOREIGN KEY ("member_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_memberships" ADD CONSTRAINT "FK_1204270ae8da270a5ce37d43fe5" FOREIGN KEY ("page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_memberships" ADD CONSTRAINT "FK_0533872145d780215da8fdcd2d5" FOREIGN KEY ("inviter_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_subscribers" ADD CONSTRAINT "FK_70c77791f6d57f1d8c9b296e8d5" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_subscribers" ADD CONSTRAINT "FK_a8262b479407eb4e8ceb2abed38" FOREIGN KEY ("page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_notifications" ADD CONSTRAINT "FK_ae9b1d1f1fe780ef8e3e7d0c0f6" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_notifications" ADD CONSTRAINT "FK_944431ae979397c8b56a99bf024" FOREIGN KEY ("notification_id") REFERENCES "notifications"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_notifications" ADD CONSTRAINT "FK_0bd036272e684627c07335edac4" FOREIGN KEY ("page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pages" ADD CONSTRAINT "FK_373c372abf9167af1ecebecb599" FOREIGN KEY ("location_id") REFERENCES "locations"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pages" ADD CONSTRAINT "FK_ccaa2a994a61f87a0a00c7f0191" FOREIGN KEY ("creator_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_tags" ADD CONSTRAINT "FK_b56b9e8a531607c834b46189d44" FOREIGN KEY ("page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "page_tags" ADD CONSTRAINT "FK_794edfebf4ad0ebc5914ffa780f" FOREIGN KEY ("hobby_id") REFERENCES "hobbies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_hobbies" ADD CONSTRAINT "FK_3d8a2d6d51c448b8fe5b12570ef" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_hobbies" ADD CONSTRAINT "FK_1c7d134fb4dc926ae7b56753744" FOREIGN KEY ("hobby_id") REFERENCES "hobbies"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "hobbies" ADD CONSTRAINT "FK_824593a5bd9c37de2d398d75acf" FOREIGN KEY ("parent_hobby_id") REFERENCES "hobbies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_dates" ADD CONSTRAINT "FK_4de3d7d3260fb83fd7a098b16ea" FOREIGN KEY ("ivent_id") REFERENCES "ivents"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_favorites" ADD CONSTRAINT "FK_5238ce0a21cc77dc16c8efe3d36" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_favorites" ADD CONSTRAINT "FK_94c70d57d4c0d611f943869dfb6" FOREIGN KEY ("favorited_ivent_id") REFERENCES "ivents"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivents" ADD CONSTRAINT "FK_d9007c737714bb3527cb0908ac9" FOREIGN KEY ("creator_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivents" ADD CONSTRAINT "FK_019c53cb4b39136adef777b25d0" FOREIGN KEY ("creator_page_id") REFERENCES "pages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivents" ADD CONSTRAINT "FK_972f51f4b19c2d3d53074be2c33" FOREIGN KEY ("creator_distributor_id") REFERENCES "distributors"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivents" ADD CONSTRAINT "FK_2531d201f3c6a8487b986a0a887" FOREIGN KEY ("location_id") REFERENCES "locations"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivents" ADD CONSTRAINT "FK_e17ed8bbbbd2fba5d182c8b2fcc" FOREIGN KEY ("vibe_folder_id") REFERENCES "vibe_folders"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivents" ADD CONSTRAINT "FK_7700168dc08d3a2b8442e0128d6" FOREIGN KEY ("memory_folder_id") REFERENCES "memory_folders"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivents" ADD CONSTRAINT "FK_9b4cae6440d405683b721af81b9" FOREIGN KEY ("category_tag_id") REFERENCES "hobbies"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_groups" ADD CONSTRAINT "FK_4d53a64d7e6527542757dd15541" FOREIGN KEY ("ivent_id") REFERENCES "ivents"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ivent_groups" ADD CONSTRAINT "FK_4fdec3c0a3fbb088bcd2d494db9" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "groups" ADD CONSTRAINT "FK_33b49cd404bac777f795028c3b0" FOREIGN KEY ("creator_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_memberships" ADD CONSTRAINT "FK_91c350b6bef466595185208322e" FOREIGN KEY ("member_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_memberships" ADD CONSTRAINT "FK_cad344fe877fcee0ac7e065ed05" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_memberships" ADD CONSTRAINT "FK_7ae4ed44151cfe259297a5bb9e6" FOREIGN KEY ("inviter_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_contacts" ADD CONSTRAINT "FK_a81491e712124db8d5423803ecb" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_followers" ADD CONSTRAINT "FK_0092daece8ed943fec27d37c413" FOREIGN KEY ("following_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_followers" ADD CONSTRAINT "FK_da722d93356ae3119d6be40d988" FOREIGN KEY ("follower_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_relationships" ADD CONSTRAINT "FK_e1bd19066147685989d72481b49" FOREIGN KEY ("sender_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_relationships" ADD CONSTRAINT "FK_3d708d464ce8dc07d04b6e023f7" FOREIGN KEY ("receiver_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_subscribers" ADD CONSTRAINT "FK_b65068c71c53133980c0058093e" FOREIGN KEY ("subscriber_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_subscribers" ADD CONSTRAINT "FK_baf26e50216c5cc1b91e6d3b9b3" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_c6390b37f4bb598bfb8309c8516" FOREIGN KEY ("university_code") REFERENCES "universities"("university_code") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_82b6aead02c99a805ae57499efe" FOREIGN KEY ("vibe_id") REFERENCES "vibes"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_7761ee03973c7c9375b032ca676" FOREIGN KEY ("creator_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`CREATE VIEW "ivent_users" AS 
    SELECT
        i.id AS ivent_id,
        COALESCE(ic.id, sm.squad_id) AS id,
        COALESCE(
            ic.collab_page_id,
            ic.collab_user_id,
            sm.member_id
        ) AS account_id,
        (
            CASE
                WHEN (ic.collab_page_id IS NOT NULL) THEN 'page'
                WHEN (ic.collab_user_id IS NOT NULL) THEN 'user'
                WHEN (sm.member_id IS NOT NULL) THEN 'member'
            END
        )::public.ivent_user_type_enum AS type,
        COALESCE(
            ic.status::TEXT,
            sm.status::TEXT
        )::public.ivent_user_status_enum AS status,
        COALESCE(ic.inviter_id, sm.inviter_id) AS inviter_id
    FROM public.ivents i
        LEFT JOIN public.ivent_collabs ic ON ic.ivent_id = i.id
        LEFT JOIN public.squads s ON s.ivent_id = i.id
        LEFT JOIN public.squad_memberships sm ON sm.squad_id = s.id
    WHERE
        COALESCE(
            ic.collab_page_id,
            ic.collab_user_id,
            sm.member_id
        ) IS NOT NULL
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","ivent_users","SELECT\n        i.id AS ivent_id,\n        COALESCE(ic.id, sm.squad_id) AS id,\n        COALESCE(\n            ic.collab_page_id,\n            ic.collab_user_id,\n            sm.member_id\n        ) AS account_id,\n        (\n            CASE\n                WHEN (ic.collab_page_id IS NOT NULL) THEN 'page'\n                WHEN (ic.collab_user_id IS NOT NULL) THEN 'user'\n                WHEN (sm.member_id IS NOT NULL) THEN 'member'\n            END\n        )::public.ivent_user_type_enum AS type,\n        COALESCE(\n            ic.status::TEXT,\n            sm.status::TEXT\n        )::public.ivent_user_status_enum AS status,\n        COALESCE(ic.inviter_id, sm.inviter_id) AS inviter_id\n    FROM public.ivents i\n        LEFT JOIN public.ivent_collabs ic ON ic.ivent_id = i.id\n        LEFT JOIN public.squads s ON s.ivent_id = i.id\n        LEFT JOIN public.squad_memberships sm ON sm.squad_id = s.id\n    WHERE\n        COALESCE(\n            ic.collab_page_id,\n            ic.collab_user_id,\n            sm.member_id\n        ) IS NOT NULL"]);
        await queryRunner.query(`CREATE VIEW "active_session_ivents" AS 
    SELECT
        account_id,
        ivent_id,
        CASE
            WHEN type = 'member' THEN 'joined'
            WHEN type IN ('page', 'user') THEN 'created'
        END AS view_type
    FROM ivent_users
    WHERE
        status IN ('accepted', 'joined', 'admin')
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","active_session_ivents","SELECT\n        account_id,\n        ivent_id,\n        CASE\n            WHEN type = 'member' THEN 'joined'\n            WHEN type IN ('page', 'user') THEN 'created'\n        END AS view_type\n    FROM ivent_users\n    WHERE\n        status IN ('accepted', 'joined', 'admin')"]);
        await queryRunner.query(`CREATE VIEW "collab_summary_of_ivent" AS 
    SELECT
        iu.account_id AS account_id,
        iu.ivent_id AS ivent_id,
        count(iu.account_id) AS collab_count,
        array_to_string(
            (
                array_agg(
                    COALESCE(p.page_name, u.username)
                )
            ) [:3],
            ','
        ) AS collab_names,
        array_to_string(
            (
                array_agg(
                    COALESCE(p.thumbnail_url, u.avatar_url)
                )
            ) [:6],
            ','
        ) AS collab_thumbnail_urls
    FROM
        ivent_users iu
        LEFT JOIN pages p ON p.id = iu.account_id
        LEFT JOIN users u ON u.id = iu.account_id
    WHERE
        iu.type IN ('page', 'user')
        AND iu.status IN ('accepted', 'admin')
    GROUP BY
        iu.ivent_id,
        iu.account_id
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","collab_summary_of_ivent","SELECT\n        iu.account_id AS account_id,\n        iu.ivent_id AS ivent_id,\n        count(iu.account_id) AS collab_count,\n        array_to_string(\n            (\n                array_agg(\n                    COALESCE(p.page_name, u.username)\n                )\n            ) [:3],\n            ','\n        ) AS collab_names,\n        array_to_string(\n            (\n                array_agg(\n                    COALESCE(p.thumbnail_url, u.avatar_url)\n                )\n            ) [:6],\n            ','\n        ) AS collab_thumbnail_urls\n    FROM\n        ivent_users iu\n        LEFT JOIN pages p ON p.id = iu.account_id\n        LEFT JOIN users u ON u.id = iu.account_id\n    WHERE\n        iu.type IN ('page', 'user')\n        AND iu.status IN ('accepted', 'admin')\n    GROUP BY\n        iu.ivent_id,\n        iu.account_id"]);
        await queryRunner.query(`CREATE VIEW "ivent_dates_aggregated" AS 
    SELECT string_agg(
            ivent_date::TEXT, ','
            ORDER BY ivent_date
        ) AS date, ivent_id
    FROM ivent_dates
    GROUP BY
        ivent_id
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","ivent_dates_aggregated","SELECT string_agg(\n            ivent_date::TEXT, ','\n            ORDER BY ivent_date\n        ) AS date, ivent_id\n    FROM ivent_dates\n    GROUP BY\n        ivent_id"]);
        await queryRunner.query(`CREATE VIEW "ivent_tags_aggregated" AS 
    SELECT string_agg(
            DISTINCT h.hobby_name, ','
        ) AS tags, it.ivent_id
    FROM ivent_tags it
        LEFT JOIN hobbies h ON h.id = it.hobby_id
    GROUP BY
        it.ivent_id
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","ivent_tags_aggregated","SELECT string_agg(\n            DISTINCT h.hobby_name, ','\n        ) AS tags, it.ivent_id\n    FROM ivent_tags it\n        LEFT JOIN hobbies h ON h.id = it.hobby_id\n    GROUP BY\n        it.ivent_id"]);
        await queryRunner.query(`CREATE VIEW "squad_friendships" AS 
    SELECT
        sm_main.member_id,
        sm_main.squad_id,
        sm_other.member_id AS other_member_id,
        COALESCE(
            sm_other.updated_at,
            sm_other.created_at
        ) AS added_at
    FROM
        squad_memberships sm_main
        LEFT JOIN squad_memberships sm_other ON sm_other.squad_id = sm_main.squad_id
    WHERE
        sm_main.status IN ('joined', 'accepted')
        AND sm_main.member_id <> sm_other.member_id
    ORDER BY sm_main.member_id, sm_main.squad_id
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","squad_friendships","SELECT\n        sm_main.member_id,\n        sm_main.squad_id,\n        sm_other.member_id AS other_member_id,\n        COALESCE(\n            sm_other.updated_at,\n            sm_other.created_at\n        ) AS added_at\n    FROM\n        squad_memberships sm_main\n        LEFT JOIN squad_memberships sm_other ON sm_other.squad_id = sm_main.squad_id\n    WHERE\n        sm_main.status IN ('joined', 'accepted')\n        AND sm_main.member_id <> sm_other.member_id\n    ORDER BY sm_main.member_id, sm_main.squad_id"]);
        await queryRunner.query(`CREATE VIEW "member_summary_of_session_squad" AS 
    SELECT
        sf.member_id AS user_id,
        s.ivent_id,
        count(u.id) AS member_count,
        array_to_string(
            (array_agg(u.firstname)) [:2],
            ','
        ) AS member_first_names,
        array_to_string(
            (array_agg(u.avatar_url)) [:5],
            ','
        ) AS member_avatar_urls
    FROM
        squad_friendships sf
        LEFT JOIN squads s ON s.id = sf.squad_id
        LEFT JOIN users u ON u.id = sf.other_member_id
    GROUP BY
        s.ivent_id,
        sf.member_id
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","member_summary_of_session_squad","SELECT\n        sf.member_id AS user_id,\n        s.ivent_id,\n        count(u.id) AS member_count,\n        array_to_string(\n            (array_agg(u.firstname)) [:2],\n            ','\n        ) AS member_first_names,\n        array_to_string(\n            (array_agg(u.avatar_url)) [:5],\n            ','\n        ) AS member_avatar_urls\n    FROM\n        squad_friendships sf\n        LEFT JOIN squads s ON s.id = sf.squad_id\n        LEFT JOIN users u ON u.id = sf.other_member_id\n    GROUP BY\n        s.ivent_id,\n        sf.member_id"]);
        await queryRunner.query(`CREATE VIEW "participant_summary_of_ivent" AS 
    SELECT
        iu.ivent_id,
        count(u.id) AS participant_count,
        array_to_string(
            (array_agg(u.firstname)) [:2],
            ','
        ) AS participant_first_names,
        array_to_string(
            (array_agg(u.avatar_url)) [:5],
            ','
        ) AS participant_avatar_urls
    FROM ivent_users iu
        LEFT JOIN users u ON u.id = iu.account_id
    WHERE
        iu.type = 'member'
        AND iu.status IN ('accepted', 'joined')
    GROUP BY
        iu.ivent_id
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","participant_summary_of_ivent","SELECT\n        iu.ivent_id,\n        count(u.id) AS participant_count,\n        array_to_string(\n            (array_agg(u.firstname)) [:2],\n            ','\n        ) AS participant_first_names,\n        array_to_string(\n            (array_agg(u.avatar_url)) [:5],\n            ','\n        ) AS participant_avatar_urls\n    FROM ivent_users iu\n        LEFT JOIN users u ON u.id = iu.account_id\n    WHERE\n        iu.type = 'member'\n        AND iu.status IN ('accepted', 'joined')\n    GROUP BY\n        iu.ivent_id"]);
        await queryRunner.query(`CREATE VIEW "user_friendships" AS 
    SELECT
        CASE
            WHEN (ur.sender_id = u.id) THEN ur.sender_id
            ELSE ur.receiver_id
        END AS user_id,
        CASE
            WHEN (ur.sender_id = u.id) THEN ur.receiver_id
            ELSE ur.sender_id
        END AS friend_id,
        COALESCE(ur.updated_at, ur.created_at) AS added_at,
        ur.status
    FROM
        user_relationships ur
        LEFT JOIN users u ON ur.sender_id = u.id
        OR ur.receiver_id = u.id
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","user_friendships","SELECT\n        CASE\n            WHEN (ur.sender_id = u.id) THEN ur.sender_id\n            ELSE ur.receiver_id\n        END AS user_id,\n        CASE\n            WHEN (ur.sender_id = u.id) THEN ur.receiver_id\n            ELSE ur.sender_id\n        END AS friend_id,\n        COALESCE(ur.updated_at, ur.created_at) AS added_at,\n        ur.status\n    FROM\n        user_relationships ur\n        LEFT JOIN users u ON ur.sender_id = u.id\n        OR ur.receiver_id = u.id"]);
        await queryRunner.query(`CREATE VIEW "session_friend_summary_of_ivent" AS 
    SELECT
        uf.user_id,
        iu.ivent_id,
        count(u.id) AS friend_count,
        array_to_string(
            (array_agg(u.firstname)) [:2],
            ','
        ) AS friend_first_names,
        array_to_string(
            (array_agg(u.avatar_url)) [:5],
            ','
        ) AS friend_avatar_urls
    FROM
        user_friendships uf
        JOIN ivent_users iu ON iu.account_id = uf.friend_id
        LEFT JOIN users u ON u.id = iu.account_id
    WHERE
        iu.type = 'member'
        AND iu.status IN ('accepted', 'joined')
    GROUP BY
        iu.ivent_id,
        uf.user_id
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","session_friend_summary_of_ivent","SELECT\n        uf.user_id,\n        iu.ivent_id,\n        count(u.id) AS friend_count,\n        array_to_string(\n            (array_agg(u.firstname)) [:2],\n            ','\n        ) AS friend_first_names,\n        array_to_string(\n            (array_agg(u.avatar_url)) [:5],\n            ','\n        ) AS friend_avatar_urls\n    FROM\n        user_friendships uf\n        JOIN ivent_users iu ON iu.account_id = uf.friend_id\n        LEFT JOIN users u ON u.id = iu.account_id\n    WHERE\n        iu.type = 'member'\n        AND iu.status IN ('accepted', 'joined')\n    GROUP BY\n        iu.ivent_id,\n        uf.user_id"]);
        await queryRunner.query(`CREATE VIEW "user_profile_stats" AS 
    WITH
        user_ivent_stats AS (
            SELECT count(ivent_users.ivent_id) AS ivent_count, ivent_users.account_id
            FROM ivent_users
            WHERE
                ivent_users.status IN ('accepted', 'joined', 'admin')
            GROUP BY
                ivent_users.account_id
        ),
        user_friend_stats AS (
            SELECT count(user_friendships.friend_id) AS friend_count, user_friendships.user_id
            FROM user_friendships
            GROUP BY
                user_friendships.user_id
        ),
        user_follower_stats AS (
            SELECT count(user_followers.follower_id) AS follower_count, user_followers.following_id
            FROM user_followers
            GROUP BY
                user_followers.following_id
        ),
        user_hobby_list AS (
            SELECT string_agg(DISTINCT h.hobby_name, ',') AS hobbies, uh.user_id
            FROM user_hobbies uh
                LEFT JOIN hobbies h ON h.id = uh.hobby_id
            GROUP BY
                uh.user_id
        )
    SELECT
        u.id AS user_id,
        concat(u.firstname, ' ', u.lastname) AS fullname,
        COALESCE(uis.ivent_count, 0) AS ivent_count,
        COALESCE(ufrs.friend_count, 0) AS friend_count,
        COALESCE(ufos.follower_count, 0) AS follower_count,
        COALESCE(uhl.hobbies, '') AS hobbies
    FROM
        public.users u
        LEFT JOIN user_ivent_stats uis ON uis.account_id = u.id
        LEFT JOIN user_friend_stats ufrs ON ufrs.user_id = u.id
        LEFT JOIN user_follower_stats ufos ON ufos.following_id = u.id
        LEFT JOIN user_hobby_list uhl ON uhl.user_id = u.id
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","user_profile_stats","WITH\n        user_ivent_stats AS (\n            SELECT count(ivent_users.ivent_id) AS ivent_count, ivent_users.account_id\n            FROM ivent_users\n            WHERE\n                ivent_users.status IN ('accepted', 'joined', 'admin')\n            GROUP BY\n                ivent_users.account_id\n        ),\n        user_friend_stats AS (\n            SELECT count(user_friendships.friend_id) AS friend_count, user_friendships.user_id\n            FROM user_friendships\n            GROUP BY\n                user_friendships.user_id\n        ),\n        user_follower_stats AS (\n            SELECT count(user_followers.follower_id) AS follower_count, user_followers.following_id\n            FROM user_followers\n            GROUP BY\n                user_followers.following_id\n        ),\n        user_hobby_list AS (\n            SELECT string_agg(DISTINCT h.hobby_name, ',') AS hobbies, uh.user_id\n            FROM user_hobbies uh\n                LEFT JOIN hobbies h ON h.id = uh.hobby_id\n            GROUP BY\n                uh.user_id\n        )\n    SELECT\n        u.id AS user_id,\n        concat(u.firstname, ' ', u.lastname) AS fullname,\n        COALESCE(uis.ivent_count, 0) AS ivent_count,\n        COALESCE(ufrs.friend_count, 0) AS friend_count,\n        COALESCE(ufos.follower_count, 0) AS follower_count,\n        COALESCE(uhl.hobbies, '') AS hobbies\n    FROM\n        public.users u\n        LEFT JOIN user_ivent_stats uis ON uis.account_id = u.id\n        LEFT JOIN user_friend_stats ufrs ON ufrs.user_id = u.id\n        LEFT JOIN user_follower_stats ufos ON ufos.following_id = u.id\n        LEFT JOIN user_hobby_list uhl ON uhl.user_id = u.id"]);
        await queryRunner.query(`CREATE VIEW "vibe_rankings" AS 
    SELECT
        id,
        vibe_folder_id,
        row_number() OVER (
            PARTITION BY
                vibe_folder_id
            ORDER BY created_at
        ) AS vibe_index,
        count(*) OVER (
            PARTITION BY
                vibe_folder_id
        ) AS vibe_count,
        lead(id) OVER (
            PARTITION BY
                vibe_folder_id
            ORDER BY created_at
        ) AS next_vibe_id,
        lag(id) OVER (
            PARTITION BY
                vibe_folder_id
            ORDER BY created_at
        ) AS previous_vibe_id
    FROM vibes
  `);
        await queryRunner.query(`INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES (DEFAULT, $1, DEFAULT, $2, $3, $4)`, ["public","VIEW","vibe_rankings","SELECT\n        id,\n        vibe_folder_id,\n        row_number() OVER (\n            PARTITION BY\n                vibe_folder_id\n            ORDER BY created_at\n        ) AS vibe_index,\n        count(*) OVER (\n            PARTITION BY\n                vibe_folder_id\n        ) AS vibe_count,\n        lead(id) OVER (\n            PARTITION BY\n                vibe_folder_id\n            ORDER BY created_at\n        ) AS next_vibe_id,\n        lag(id) OVER (\n            PARTITION BY\n                vibe_folder_id\n            ORDER BY created_at\n        ) AS previous_vibe_id\n    FROM vibes"]);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","vibe_rankings","public"]);
        await queryRunner.query(`DROP VIEW "vibe_rankings"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","user_profile_stats","public"]);
        await queryRunner.query(`DROP VIEW "user_profile_stats"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","session_friend_summary_of_ivent","public"]);
        await queryRunner.query(`DROP VIEW "session_friend_summary_of_ivent"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","user_friendships","public"]);
        await queryRunner.query(`DROP VIEW "user_friendships"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","participant_summary_of_ivent","public"]);
        await queryRunner.query(`DROP VIEW "participant_summary_of_ivent"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","member_summary_of_session_squad","public"]);
        await queryRunner.query(`DROP VIEW "member_summary_of_session_squad"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","squad_friendships","public"]);
        await queryRunner.query(`DROP VIEW "squad_friendships"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","ivent_tags_aggregated","public"]);
        await queryRunner.query(`DROP VIEW "ivent_tags_aggregated"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","ivent_dates_aggregated","public"]);
        await queryRunner.query(`DROP VIEW "ivent_dates_aggregated"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","collab_summary_of_ivent","public"]);
        await queryRunner.query(`DROP VIEW "collab_summary_of_ivent"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","active_session_ivents","public"]);
        await queryRunner.query(`DROP VIEW "active_session_ivents"`);
        await queryRunner.query(`DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "schema" = $3`, ["VIEW","ivent_users","public"]);
        await queryRunner.query(`DROP VIEW "ivent_users"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_7761ee03973c7c9375b032ca676"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_82b6aead02c99a805ae57499efe"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_c6390b37f4bb598bfb8309c8516"`);
        await queryRunner.query(`ALTER TABLE "user_subscribers" DROP CONSTRAINT "FK_baf26e50216c5cc1b91e6d3b9b3"`);
        await queryRunner.query(`ALTER TABLE "user_subscribers" DROP CONSTRAINT "FK_b65068c71c53133980c0058093e"`);
        await queryRunner.query(`ALTER TABLE "user_relationships" DROP CONSTRAINT "FK_3d708d464ce8dc07d04b6e023f7"`);
        await queryRunner.query(`ALTER TABLE "user_relationships" DROP CONSTRAINT "FK_e1bd19066147685989d72481b49"`);
        await queryRunner.query(`ALTER TABLE "user_followers" DROP CONSTRAINT "FK_da722d93356ae3119d6be40d988"`);
        await queryRunner.query(`ALTER TABLE "user_followers" DROP CONSTRAINT "FK_0092daece8ed943fec27d37c413"`);
        await queryRunner.query(`ALTER TABLE "user_contacts" DROP CONSTRAINT "FK_a81491e712124db8d5423803ecb"`);
        await queryRunner.query(`ALTER TABLE "group_memberships" DROP CONSTRAINT "FK_7ae4ed44151cfe259297a5bb9e6"`);
        await queryRunner.query(`ALTER TABLE "group_memberships" DROP CONSTRAINT "FK_cad344fe877fcee0ac7e065ed05"`);
        await queryRunner.query(`ALTER TABLE "group_memberships" DROP CONSTRAINT "FK_91c350b6bef466595185208322e"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP CONSTRAINT "FK_33b49cd404bac777f795028c3b0"`);
        await queryRunner.query(`ALTER TABLE "ivent_groups" DROP CONSTRAINT "FK_4fdec3c0a3fbb088bcd2d494db9"`);
        await queryRunner.query(`ALTER TABLE "ivent_groups" DROP CONSTRAINT "FK_4d53a64d7e6527542757dd15541"`);
        await queryRunner.query(`ALTER TABLE "ivents" DROP CONSTRAINT "FK_9b4cae6440d405683b721af81b9"`);
        await queryRunner.query(`ALTER TABLE "ivents" DROP CONSTRAINT "FK_7700168dc08d3a2b8442e0128d6"`);
        await queryRunner.query(`ALTER TABLE "ivents" DROP CONSTRAINT "FK_e17ed8bbbbd2fba5d182c8b2fcc"`);
        await queryRunner.query(`ALTER TABLE "ivents" DROP CONSTRAINT "FK_2531d201f3c6a8487b986a0a887"`);
        await queryRunner.query(`ALTER TABLE "ivents" DROP CONSTRAINT "FK_972f51f4b19c2d3d53074be2c33"`);
        await queryRunner.query(`ALTER TABLE "ivents" DROP CONSTRAINT "FK_019c53cb4b39136adef777b25d0"`);
        await queryRunner.query(`ALTER TABLE "ivents" DROP CONSTRAINT "FK_d9007c737714bb3527cb0908ac9"`);
        await queryRunner.query(`ALTER TABLE "user_favorites" DROP CONSTRAINT "FK_94c70d57d4c0d611f943869dfb6"`);
        await queryRunner.query(`ALTER TABLE "user_favorites" DROP CONSTRAINT "FK_5238ce0a21cc77dc16c8efe3d36"`);
        await queryRunner.query(`ALTER TABLE "ivent_dates" DROP CONSTRAINT "FK_4de3d7d3260fb83fd7a098b16ea"`);
        await queryRunner.query(`ALTER TABLE "hobbies" DROP CONSTRAINT "FK_824593a5bd9c37de2d398d75acf"`);
        await queryRunner.query(`ALTER TABLE "user_hobbies" DROP CONSTRAINT "FK_1c7d134fb4dc926ae7b56753744"`);
        await queryRunner.query(`ALTER TABLE "user_hobbies" DROP CONSTRAINT "FK_3d8a2d6d51c448b8fe5b12570ef"`);
        await queryRunner.query(`ALTER TABLE "page_tags" DROP CONSTRAINT "FK_794edfebf4ad0ebc5914ffa780f"`);
        await queryRunner.query(`ALTER TABLE "page_tags" DROP CONSTRAINT "FK_b56b9e8a531607c834b46189d44"`);
        await queryRunner.query(`ALTER TABLE "pages" DROP CONSTRAINT "FK_ccaa2a994a61f87a0a00c7f0191"`);
        await queryRunner.query(`ALTER TABLE "pages" DROP CONSTRAINT "FK_373c372abf9167af1ecebecb599"`);
        await queryRunner.query(`ALTER TABLE "user_notifications" DROP CONSTRAINT "FK_0bd036272e684627c07335edac4"`);
        await queryRunner.query(`ALTER TABLE "user_notifications" DROP CONSTRAINT "FK_944431ae979397c8b56a99bf024"`);
        await queryRunner.query(`ALTER TABLE "user_notifications" DROP CONSTRAINT "FK_ae9b1d1f1fe780ef8e3e7d0c0f6"`);
        await queryRunner.query(`ALTER TABLE "page_subscribers" DROP CONSTRAINT "FK_a8262b479407eb4e8ceb2abed38"`);
        await queryRunner.query(`ALTER TABLE "page_subscribers" DROP CONSTRAINT "FK_70c77791f6d57f1d8c9b296e8d5"`);
        await queryRunner.query(`ALTER TABLE "page_memberships" DROP CONSTRAINT "FK_0533872145d780215da8fdcd2d5"`);
        await queryRunner.query(`ALTER TABLE "page_memberships" DROP CONSTRAINT "FK_1204270ae8da270a5ce37d43fe5"`);
        await queryRunner.query(`ALTER TABLE "page_memberships" DROP CONSTRAINT "FK_3f5bb0581d77f2d1a4767148cc4"`);
        await queryRunner.query(`ALTER TABLE "page_followers" DROP CONSTRAINT "FK_7301faa62f4cfbdd9e931e715e1"`);
        await queryRunner.query(`ALTER TABLE "page_followers" DROP CONSTRAINT "FK_53d741cc7ddb64911edb7aaa218"`);
        await queryRunner.query(`ALTER TABLE "page_blocks_by_user" DROP CONSTRAINT "FK_b99e8f60ef3779041e78d892711"`);
        await queryRunner.query(`ALTER TABLE "page_blocks_by_user" DROP CONSTRAINT "FK_4cb6bada343671ad567f7faaa9d"`);
        await queryRunner.query(`ALTER TABLE "page_blacklists" DROP CONSTRAINT "FK_153fab56c1c0b8b80a23c1590a7"`);
        await queryRunner.query(`ALTER TABLE "page_blacklists" DROP CONSTRAINT "FK_90249012c8e5feb289f1bb6672e"`);
        await queryRunner.query(`ALTER TABLE "page_blacklists" DROP CONSTRAINT "FK_7aaf629d46723b7346d6701577b"`);
        await queryRunner.query(`ALTER TABLE "universities" DROP CONSTRAINT "FK_2ca32288ef68bfc2d1e1dd3f304"`);
        await queryRunner.query(`ALTER TABLE "ivent_universities" DROP CONSTRAINT "FK_2d3e8cd913716c7563ca643d9d9"`);
        await queryRunner.query(`ALTER TABLE "ivent_universities" DROP CONSTRAINT "FK_ed20249c7a3e0cf4b7bc1a1e808"`);
        await queryRunner.query(`ALTER TABLE "ivent_collabs" DROP CONSTRAINT "FK_fc60b10934fa5b533b012ca76c0"`);
        await queryRunner.query(`ALTER TABLE "ivent_collabs" DROP CONSTRAINT "FK_c5f1cb13a61d21aad3a20758b2e"`);
        await queryRunner.query(`ALTER TABLE "ivent_collabs" DROP CONSTRAINT "FK_a035776161490da96990b4aac92"`);
        await queryRunner.query(`ALTER TABLE "ivent_collabs" DROP CONSTRAINT "FK_a5da1d0158e5088d3f9e7c78855"`);
        await queryRunner.query(`ALTER TABLE "squad_memberships" DROP CONSTRAINT "FK_7b9c958ac5efa4631190b644f3e"`);
        await queryRunner.query(`ALTER TABLE "squad_memberships" DROP CONSTRAINT "FK_63480c6e49a2d97ce95370654bc"`);
        await queryRunner.query(`ALTER TABLE "squad_memberships" DROP CONSTRAINT "FK_089814640020507508563ec412d"`);
        await queryRunner.query(`ALTER TABLE "squads" DROP CONSTRAINT "FK_33902dbbe9294dba4df78acbaf6"`);
        await queryRunner.query(`ALTER TABLE "squads" DROP CONSTRAINT "FK_50556d38ebc8c8688dcb54c3495"`);
        await queryRunner.query(`ALTER TABLE "squads" DROP CONSTRAINT "FK_f039b8c83ea780372366b8d9857"`);
        await queryRunner.query(`ALTER TABLE "squads" DROP CONSTRAINT "FK_695ffc086f02e03f621415c1985"`);
        await queryRunner.query(`ALTER TABLE "vibes" DROP CONSTRAINT "FK_39bc36724aedec7b61f0d415d88"`);
        await queryRunner.query(`ALTER TABLE "vibes" DROP CONSTRAINT "FK_1c65f10879a29fa23da49b7c2ee"`);
        await queryRunner.query(`ALTER TABLE "vibes" DROP CONSTRAINT "FK_e3d8164f525270eb60ffcedcd96"`);
        await queryRunner.query(`ALTER TABLE "vibe_views" DROP CONSTRAINT "FK_ec34289ebe49b3b1d5aab1c53f1"`);
        await queryRunner.query(`ALTER TABLE "vibe_views" DROP CONSTRAINT "FK_8d2363265646fe33c79e05789ec"`);
        await queryRunner.query(`ALTER TABLE "vibe_likes" DROP CONSTRAINT "FK_ce2e92af19464c8da031296c8a2"`);
        await queryRunner.query(`ALTER TABLE "vibe_likes" DROP CONSTRAINT "FK_1c7a559f665b286b896670104ed"`);
        await queryRunner.query(`ALTER TABLE "memories" DROP CONSTRAINT "FK_963291cacc03e98f36343f866e7"`);
        await queryRunner.query(`ALTER TABLE "memories" DROP CONSTRAINT "FK_f78a58bd5fadaabd7148f27789d"`);
        await queryRunner.query(`ALTER TABLE "memories" DROP CONSTRAINT "FK_34c0e97a21d2602c2fed3fc6af3"`);
        await queryRunner.query(`ALTER TABLE "ivent_tags" DROP CONSTRAINT "FK_db2faba6b45158bf84482178478"`);
        await queryRunner.query(`ALTER TABLE "ivent_tags" DROP CONSTRAINT "FK_e306d6f64d1638ab5d7394bfac8"`);
        await queryRunner.query(`DROP TABLE "comments"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TYPE "public"."users_friends_privacy_enum"`);
        await queryRunner.query(`DROP TYPE "public"."users_ivent_privacy_enum"`);
        await queryRunner.query(`DROP TYPE "public"."users_gender_enum"`);
        await queryRunner.query(`DROP TYPE "public"."users_edu_verification_enum"`);
        await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
        await queryRunner.query(`DROP TABLE "user_subscribers"`);
        await queryRunner.query(`DROP TABLE "user_relationships"`);
        await queryRunner.query(`DROP TYPE "public"."user_relationships_status_enum"`);
        await queryRunner.query(`DROP TABLE "user_followers"`);
        await queryRunner.query(`DROP TYPE "public"."user_followers_status_enum"`);
        await queryRunner.query(`DROP TABLE "user_contacts"`);
        await queryRunner.query(`DROP TABLE "group_memberships"`);
        await queryRunner.query(`DROP TYPE "public"."group_memberships_status_enum"`);
        await queryRunner.query(`DROP TABLE "groups"`);
        await queryRunner.query(`DROP TABLE "ivent_groups"`);
        await queryRunner.query(`DROP TABLE "ivents"`);
        await queryRunner.query(`DROP TYPE "public"."ivents_privacy_enum"`);
        await queryRunner.query(`DROP TYPE "public"."ivents_creator_type_enum"`);
        await queryRunner.query(`DROP TABLE "user_favorites"`);
        await queryRunner.query(`DROP TABLE "ivent_dates"`);
        await queryRunner.query(`DROP TABLE "hobbies"`);
        await queryRunner.query(`DROP TABLE "user_hobbies"`);
        await queryRunner.query(`DROP TABLE "page_tags"`);
        await queryRunner.query(`DROP TABLE "pages"`);
        await queryRunner.query(`DROP TABLE "user_notifications"`);
        await queryRunner.query(`DROP TABLE "notifications"`);
        await queryRunner.query(`DROP TYPE "public"."notifications_notification_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."notifications_status_enum"`);
        await queryRunner.query(`DROP TABLE "page_subscribers"`);
        await queryRunner.query(`DROP TABLE "page_memberships"`);
        await queryRunner.query(`DROP TYPE "public"."page_memberships_status_enum"`);
        await queryRunner.query(`DROP TABLE "page_followers"`);
        await queryRunner.query(`DROP TABLE "page_blocks_by_user"`);
        await queryRunner.query(`DROP TABLE "page_blacklists"`);
        await queryRunner.query(`DROP TABLE "locations"`);
        await queryRunner.query(`DROP TABLE "universities"`);
        await queryRunner.query(`DROP TABLE "ivent_universities"`);
        await queryRunner.query(`DROP TABLE "ivent_collabs"`);
        await queryRunner.query(`DROP TYPE "public"."ivent_collabs_collab_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."ivent_collabs_status_enum"`);
        await queryRunner.query(`DROP TABLE "squad_memberships"`);
        await queryRunner.query(`DROP TYPE "public"."squad_memberships_status_enum"`);
        await queryRunner.query(`DROP TABLE "squads"`);
        await queryRunner.query(`DROP TABLE "vibe_folders"`);
        await queryRunner.query(`DROP TABLE "vibes"`);
        await queryRunner.query(`DROP TYPE "public"."vibes_privacy_enum"`);
        await queryRunner.query(`DROP TYPE "public"."vibes_creator_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."vibes_media_format_enum"`);
        await queryRunner.query(`DROP TABLE "vibe_views"`);
        await queryRunner.query(`DROP TABLE "vibe_likes"`);
        await queryRunner.query(`DROP TABLE "memory_folders"`);
        await queryRunner.query(`DROP TABLE "memories"`);
        await queryRunner.query(`DROP TYPE "public"."memories_creator_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."memories_media_format_enum"`);
        await queryRunner.query(`DROP TABLE "ivent_tags"`);
        await queryRunner.query(`DROP TABLE "distributors"`);
        await queryRunner.query(`DROP TABLE "auth"`);
    }

}
