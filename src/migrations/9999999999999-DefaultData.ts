import { readFileSync } from 'fs';
import { join } from 'path';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class DefaultData9999999999999 implements MigrationInterface {
    name = 'DefaultData9999999999999'
    public async up(queryRunner: QueryRunner): Promise<void> {
        const hobbiesSql = readFileSync(join(__dirname, '/sql/default_hobbies_data.sql'), 'utf8');
        const universitiesSql = readFileSync(join(__dirname, '/sql/default_universities_data.sql'), 'utf8');
        await queryRunner.query(hobbiesSql);
        await queryRunner.query(universitiesSql);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DELETE FROM hobbies`);
        await queryRunner.query(`DELETE FROM universities`);
    }
}