import { <PERSON>umn, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON>ty, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { NotificationStatusEnum, NotificationTypeEnum } from 'src/enums';
import { UserNotification } from './user-notification.entity';

@Entity('notifications')
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'enum',
    enum: NotificationStatusEnum,
    default: NotificationStatusEnum.ACTIVE,
  })
  status!: NotificationStatusEnum;

  @Column({ type: 'varchar' })
  account_type!: string;

  @Column({ type: 'uuid' })
  account_id!: string;

  @Column({ type: 'text', nullable: true })
  account_image_url!: string | null;

  @Column({ type: 'varchar', nullable: true })
  account_name!: string | null;

  @Column({ type: 'varchar', nullable: true })
  content_type!: string | null;

  @Column({ type: 'uuid', nullable: true })
  content_id!: string | null;

  @Column({ type: 'text', nullable: true })
  content_thumbnail_url!: string | null;

  @Column({ type: 'varchar', nullable: true })
  content_name!: string | null;

  @Column({ type: 'varchar', nullable: true })
  content_item!: string | null;

  @Column({ type: 'varchar', nullable: true })
  action_type!: string | null;

  @Column({ type: 'uuid', nullable: true })
  action_id!: string | null;

  @Column({ type: 'enum', enum: NotificationTypeEnum })
  notification_type!: NotificationTypeEnum;

  @Column({ type: 'uuid', nullable: true })
  subject_id!: string | null;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => UserNotification, (userNotification) => userNotification.notification)
  user_notifications?: UserNotification[];
}
