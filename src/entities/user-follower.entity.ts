import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { FollowerStatusEnum } from 'src/enums';
import { User } from './user.entity';

@Entity('user_followers')
export class UserFollower {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'enum',
    enum: FollowerStatusEnum,
  })
  status!: FollowerStatusEnum;

  @Column({ type: 'uuid' })
  following_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'following_id' })
  following?: User;

  @Column({ type: 'uuid' })
  follower_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'follower_id' })
  follower?: User;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
