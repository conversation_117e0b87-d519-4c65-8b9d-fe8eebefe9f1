import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>olumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AccountTypeEnum, MediaFormatEnum } from 'src/enums';
import { MemoryFolder } from './memory-folder.entity';
import { Page } from './page.entity';
import { User } from './user.entity';

@Entity('memories')
export class Memory {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'enum',
    enum: MediaFormatEnum,
  })
  media_format!: MediaFormatEnum;

  @Column({ type: 'text', nullable: true })
  thumbnail_url!: string | null;

  @Column({
    type: 'enum',
    enum: AccountTypeEnum,
  })
  creator_type!: AccountTypeEnum;

  @Column({ type: 'text', nullable: true })
  caption!: string | null;

  @Column({ type: 'uuid', nullable: true })
  creator_user_id!: string | null;

  @ManyToOne(() => User, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_user_id' })
  creator_user?: User | null;

  @Column({ type: 'uuid', nullable: true })
  creator_page_id!: string | null;

  @ManyToOne(() => Page, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_page_id' })
  creator_page?: Page | null;

  @Column({ type: 'uuid' })
  memory_folder_id!: string;

  @ManyToOne(() => MemoryFolder, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'memory_folder_id' })
  memory_folder?: MemoryFolder;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
