import { IventCreatorTypeEnum, IventPrivacyEnum, IventViewTypeEnum } from 'src/enums';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Distributor } from './distributor.entity';
import { Hobby } from './hobby.entity';
import { IventCollab } from './ivent-collab.entity';
import { IventDate } from './ivent-date.entity';
import { IventGroup } from './ivent-group.entity';
import { IventTag } from './ivent-tag.entity';
import { IventUniversity } from './ivent-university.entity';
import { Location } from './location.entity';
import { MemoryFolder } from './memory-folder.entity';
import { Page } from './page.entity';
import { Squad } from './squad.entity';
import { UserFavorite } from './user-favorite.entity';
import { User } from './user.entity';
import { VibeFolder } from './vibe-folder.entity';
import {
  ActiveSessionIvents,
  CollabSummaryOfIvent,
  IventDatesAggregated,
  IventTagsAggregated,
  IventUsers,
  MemberSummaryOfSessionSquad,
  ParticipantSummaryOfIvent,
  SessionFriendSummaryOfIvent,
} from './views';

@Entity('ivents')
export class Ivent {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar', length: 255 })
  ivent_name!: string;

  @Column({ type: 'text', nullable: true })
  thumbnail_url!: string | null;

  @Column({ type: 'enum', enum: IventCreatorTypeEnum })
  creator_type!: IventCreatorTypeEnum;

  @Column({ type: 'text', nullable: true })
  description!: string | null;

  @Column({
    type: 'enum',
    enum: IventPrivacyEnum,
    default: IventPrivacyEnum.PUBLIC,
  })
  privacy!: IventPrivacyEnum;

  @Column({ type: 'text', nullable: true })
  google_forms_url!: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true })
  instagram_username!: string | null;

  @Column({ type: 'text', nullable: true })
  whatsapp_url!: string | null;

  @Column({ type: 'boolean', default: false })
  is_whatsapp_url_private!: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  whatsapp_number!: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true })
  call_number!: string | null;

  @Column({ type: 'text', nullable: true })
  website_url!: string | null;

  @Column({ type: 'uuid', nullable: true })
  creator_user_id!: string | null;

  @ManyToOne(() => User, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_user_id' })
  creator_user?: User | null;

  @Column({ type: 'uuid', nullable: true })
  creator_page_id!: string | null;

  @ManyToOne(() => Page, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_page_id' })
  creator_page?: Page | null;

  @Column({ type: 'uuid', nullable: true })
  creator_distributor_id!: string | null;

  @ManyToOne(() => Distributor, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_distributor_id' })
  creator_distributor?: Distributor | null;

  @Column({ type: 'uuid' })
  location_id!: string;

  @ManyToOne(() => Location, { onDelete: 'RESTRICT', cascade: true })
  @JoinColumn({ name: 'location_id' })
  location?: Location;

  @Column({ type: 'uuid' })
  vibe_folder_id!: string;

  @OneToOne(() => VibeFolder, { onDelete: 'RESTRICT', cascade: true })
  @JoinColumn({ name: 'vibe_folder_id' })
  vibe_folder?: VibeFolder;

  @Column({ type: 'uuid' })
  memory_folder_id!: string;

  @OneToOne(() => MemoryFolder, { onDelete: 'RESTRICT', cascade: true })
  @JoinColumn({ name: 'memory_folder_id' })
  memory_folder?: MemoryFolder;

  @Column({ type: 'uuid' })
  category_tag_id!: string;

  @ManyToOne(() => Hobby, { onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'category_tag_id' })
  category_tag?: Hobby;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => IventCollab, (iventCollab) => iventCollab.ivent, { cascade: true })
  collabs?: IventCollab[];

  @OneToMany(() => Squad, (squad) => squad.ivent)
  squads?: Squad[];

  @OneToMany(() => IventDate, (iventDate) => iventDate.ivent, { cascade: true })
  dates?: IventDate[];

  @OneToMany(() => IventTag, (iventTag) => iventTag.ivent, { cascade: true })
  tags?: IventTag[];

  @OneToMany(() => UserFavorite, (userFavorite) => userFavorite.favorited_ivent)
  favorited_by?: UserFavorite[];

  @OneToMany(() => IventUniversity, (iventUniversity) => iventUniversity.ivent)
  universities?: IventUniversity[];

  @OneToMany(() => IventGroup, (iventGroup) => iventGroup.ivent)
  groups?: IventGroup[];

  // Additional relationships from views
  @OneToMany(() => IventUsers, (iventUsers) => iventUsers.ivent)
  private ivent_users?: IventUsers[];

  @OneToMany(() => ActiveSessionIvents, (activeSessionIvents) => activeSessionIvents.ivent)
  private active_session_ivents?: ActiveSessionIvents[];

  @OneToMany(() => CollabSummaryOfIvent, (collabSummaryOfIvent) => collabSummaryOfIvent.ivent)
  private collab_summary_of_ivent?: CollabSummaryOfIvent[];

  @OneToOne(() => IventDatesAggregated, (iventDatesAggregated) => iventDatesAggregated.ivent)
  private ivent_dates_aggregated?: IventDatesAggregated | null;

  @OneToOne(() => IventTagsAggregated, (iventTagsAggregated) => iventTagsAggregated.ivent)
  private ivent_tags_aggregated?: IventTagsAggregated | null;

  @OneToMany(() => MemberSummaryOfSessionSquad, (memberSummaryOfSessionSquad) => memberSummaryOfSessionSquad.ivent)
  private member_summary_of_session_squad?: MemberSummaryOfSessionSquad[];

  @OneToOne(() => ParticipantSummaryOfIvent, (participantSummaryOfIvent) => participantSummaryOfIvent.ivent)
  private participant_summary_of_ivent?: ParticipantSummaryOfIvent | null;

  @OneToMany(() => SessionFriendSummaryOfIvent, (sessionFriendSummaryOfIvent) => sessionFriendSummaryOfIvent.ivent)
  private session_friend_summary_of_ivent?: SessionFriendSummaryOfIvent[];

  // Additional hydratable properties
  favorite_count?: number;
  is_favorited?: boolean;

  // Additional getters
  get location_name(): string {
    return this.location!.location_name;
  }

  get latitude(): number {
    return this.location!.geom.coordinates[0];
  }

  get longitude(): number {
    return this.location!.geom.coordinates[1];
  }

  get tag_list(): string[] {
    return this.ivent_tags_aggregated?.tags || [];
  }

  get date_list(): string[] {
    return this.ivent_dates_aggregated?.date || [];
  }

  get creator_id(): string {
    switch (this.creator_type) {
      case IventCreatorTypeEnum.USER:
        return this.creator_user_id!;
      case IventCreatorTypeEnum.PAGE:
        return this.creator_page_id!;
      case IventCreatorTypeEnum.DISTRIBUTOR:
        return this.creator_distributor_id!;
    }
  }

  get creator_name(): string {
    switch (this.creator_type) {
      case IventCreatorTypeEnum.USER:
        return this.creator_user!.username;
      case IventCreatorTypeEnum.PAGE:
        return this.creator_page!.page_name;
      case IventCreatorTypeEnum.DISTRIBUTOR:
        return this.creator_distributor!.distributor_name;
    }
  }

  get creator_image_url(): string | null {
    switch (this.creator_type) {
      case IventCreatorTypeEnum.USER:
        return this.creator_user!.avatar_url;
      case IventCreatorTypeEnum.PAGE:
        return this.creator_page!.thumbnail_url;
      case IventCreatorTypeEnum.DISTRIBUTOR:
        return this.creator_distributor!.thumbnail_url;
    }
  }

  get view_type(): IventViewTypeEnum {
    return this.active_session_ivents?.[0]?.view_type || IventViewTypeEnum.DEFAULT;
  }

  get member_count(): number {
    switch (this.view_type) {
      case IventViewTypeEnum.CREATED:
        return this.participant_summary_of_ivent?.participant_count || 0;
      case IventViewTypeEnum.JOINED:
        return this.member_summary_of_session_squad?.[0]?.member_count || 0;
      case IventViewTypeEnum.DEFAULT:
        return this.session_friend_summary_of_ivent?.[0]?.friend_count || 0;
    }
  }

  get member_first_names(): string[] {
    switch (this.view_type) {
      case IventViewTypeEnum.CREATED:
        return this.participant_summary_of_ivent?.participant_first_names || [];
      case IventViewTypeEnum.JOINED:
        return this.member_summary_of_session_squad?.[0]?.member_first_names || [];
      case IventViewTypeEnum.DEFAULT:
        return this.session_friend_summary_of_ivent?.[0]?.friend_first_names || [];
    }
  }

  get member_avatar_urls(): (string | null)[] {
    switch (this.view_type) {
      case IventViewTypeEnum.CREATED:
        return this.participant_summary_of_ivent?.participant_avatar_urls || [];
      case IventViewTypeEnum.JOINED:
        return this.member_summary_of_session_squad?.[0]?.member_avatar_urls || [];
      case IventViewTypeEnum.DEFAULT:
        return this.session_friend_summary_of_ivent?.[0]?.friend_avatar_urls || [];
    }
  }

  get collab_count(): number {
    return this.collab_summary_of_ivent?.[0]?.collab_count || 0;
  }

  get collab_names(): string[] {
    return this.collab_summary_of_ivent?.[0]?.collab_names || [];
  }

  get collab_thumbnail_urls(): (string | null)[] {
    return this.collab_summary_of_ivent?.[0]?.collab_thumbnail_urls || [];
  }
}
