import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON>olumn,
  En<PERSON>ty,
  <PERSON>inColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Comment } from './comment.entity';
import {
  UserEduVerificationEnum,
  UserFriendsPrivacyEnum,
  UserGenderEnum,
  UserIventPrivacyEnum,
  UserRoleEnum,
} from 'src/enums';
import { GroupMembership } from './group-membership.entity';
import { Group } from './group.entity';
import { IventCollab } from './ivent-collab.entity';
import { Ivent } from './ivent.entity';
import { Memory } from './memory.entity';
import { PageBlacklist } from './page-blacklist.entity';
import { PageBlockByUser } from './page-block-by-user.entity';
import { PageFollower } from './page-follower.entity';
import { PageMembership } from './page-membership.entity';
import { PageSubscriber } from './page-subscriber.entity';
import { Page } from './page.entity';
import { SquadMembership } from './squad-membership.entity';
import { Squad } from './squad.entity';
import { University } from './university.entity';
import { UserContact } from './user-contact.entity';
import { UserFavorite } from './user-favorite.entity';
import { UserFollower } from './user-follower.entity';
import { UserHobby } from './user-hobby.entity';
import { UserNotification } from './user-notification.entity';
import { UserRelationship } from './user-relationship.entity';
import { UserSubscriber } from './user-subscriber.entity';
import { VibeLike } from './vibe-like.entity';
import { VibeView } from './vibe-view.entity';
import { Vibe } from './vibe.entity';
import {
  ActiveSessionIvents,
  CollabSummaryOfIvent,
  IventUsers,
  MemberSummaryOfSessionSquad,
  SessionFriendSummaryOfIvent,
  SquadFriendships,
  UserFriendships,
  UserProfileStats,
} from './views';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'enum',
    enum: UserRoleEnum,
    default: UserRoleEnum.LEVEL_0,
  })
  role!: UserRoleEnum;

  @Column({
    type: 'enum',
    enum: UserEduVerificationEnum,
    default: UserEduVerificationEnum.UNVERIFIED,
  })
  edu_verification!: UserEduVerificationEnum;

  @Column({ type: 'varchar', length: 255, nullable: true })
  email!: string;

  @Column({ type: 'varchar', length: 255 })
  username!: string;

  @Column({ type: 'varchar', length: 255 })
  phone_number!: string;

  @Column({ type: 'varchar', length: 255 })
  firstname!: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  lastname!: string | null;

  @Column({
    type: 'enum',
    enum: UserGenderEnum,
    nullable: true,
  })
  gender!: UserGenderEnum | null;

  @Column({ type: 'varchar', length: 255, nullable: true })
  birthday!: string | null;

  @Column({ type: 'varchar', length: 6, nullable: true })
  email_verification!: string | null;

  @Column({ type: 'timestamp with time zone', default: () => 'now()' })
  email_verification_expires_at!: Date;

  @Column({ type: 'text', nullable: true })
  avatar_url!: string | null;

  @Column({ type: 'boolean', default: true })
  is_push_notifications_on!: boolean;

  @Column({ type: 'boolean', default: true })
  is_email_notifications_on!: boolean;

  @Column({
    type: 'enum',
    enum: UserIventPrivacyEnum,
    default: UserIventPrivacyEnum.FRIENDS,
  })
  ivent_privacy!: UserIventPrivacyEnum;

  @Column({
    type: 'enum',
    enum: UserFriendsPrivacyEnum,
    default: UserFriendsPrivacyEnum.FRIENDS,
  })
  friends_privacy!: UserFriendsPrivacyEnum;

  @Column({ type: 'timestamp with time zone', default: () => 'now()' })
  last_login!: Date;

  @Column({ type: 'varchar', nullable: true })
  university_code!: string | null;

  @ManyToOne(() => University, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'university_code' })
  university?: University | null;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => Ivent, (ivent) => ivent.creator_user)
  created_ivents?: Ivent[];

  @OneToMany(() => Page, (page) => page.creator)
  created_pages?: Page[];

  @OneToMany(() => Memory, (memory) => memory.creator_user)
  created_memories?: Memory[];

  @OneToMany(() => Vibe, (vibe) => vibe.creator_user)
  created_vibes?: Vibe[];

  @OneToMany(() => Comment, (comment) => comment.creator)
  comments?: Comment[];

  @OneToMany(() => IventCollab, (iventCollab) => iventCollab.collab_user)
  ivent_collabs?: IventCollab[];

  @OneToMany(() => IventCollab, (iventCollab) => iventCollab.inviter)
  invited_collabs?: IventCollab[];

  @OneToMany(() => Squad, (squad) => squad.creator)
  created_squads?: Squad[];

  @OneToMany(() => SquadMembership, (squadMembership) => squadMembership.member)
  squad_memberships?: SquadMembership[];

  @OneToMany(() => SquadMembership, (squadMembership) => squadMembership.inviter)
  squad_invitations?: SquadMembership[];

  @OneToMany(() => UserHobby, (userHobby) => userHobby.user)
  hobbies?: UserHobby[];

  @OneToMany(() => UserFavorite, (userFavorite) => userFavorite.user)
  favorites?: UserFavorite[];

  @OneToMany(() => UserRelationship, (userRelationship) => userRelationship.sender)
  sent_relationship_requests?: UserRelationship[];

  @OneToMany(() => UserRelationship, (userRelationship) => userRelationship.receiver)
  received_relationship_requests?: UserRelationship[];

  @OneToMany(() => UserFollower, (userFollower) => userFollower.following)
  followers?: UserFollower[];

  @OneToMany(() => UserFollower, (userFollower) => userFollower.follower)
  following?: UserFollower[];

  @OneToMany(() => VibeLike, (vibeLike) => vibeLike.user)
  liked_vibes?: VibeLike[];

  @OneToMany(() => VibeView, (vibeView) => vibeView.user)
  viewed_vibes?: VibeView[];

  @OneToMany(() => GroupMembership, (groupMembership) => groupMembership.member)
  group_memberships?: GroupMembership[];

  @OneToMany(() => GroupMembership, (groupMembership) => groupMembership.inviter)
  group_invitations?: GroupMembership[];

  @OneToMany(() => PageBlacklist, (pageBlacklist) => pageBlacklist.blocked)
  blocked_by_pages?: PageBlacklist[];

  @OneToMany(() => PageBlacklist, (pageBlacklist) => pageBlacklist.blocker)
  page_blockings?: PageBlacklist[];

  @OneToMany(() => PageBlockByUser, (pageBlockByUser) => pageBlockByUser.user)
  blocked_pages?: PageBlockByUser[];

  @OneToMany(() => PageFollower, (pageFollower) => pageFollower.user)
  followed_pages?: PageFollower[];

  @OneToMany(() => PageMembership, (pageMembership) => pageMembership.member)
  page_memberships?: PageMembership[];

  @OneToMany(() => PageMembership, (pageMembership) => pageMembership.inviter)
  page_invitations?: PageMembership[];

  @OneToMany(() => PageSubscriber, (pageSubscriber) => pageSubscriber.user)
  page_subscriptions?: PageSubscriber[];

  @OneToMany(() => Group, (group) => group.creator)
  created_groups?: Group[];

  @OneToMany(() => UserNotification, (userNotification) => userNotification.user)
  notifications?: UserNotification[];

  @OneToMany(() => UserContact, (userContact) => userContact.user)
  contacts?: UserContact[];

  @OneToMany(() => UserSubscriber, (userSubscriber) => userSubscriber.subscriber)
  subscribed_users?: UserSubscriber[];

  @OneToMany(() => UserSubscriber, (userSubscriber) => userSubscriber.user)
  subscribers?: UserSubscriber[];

  // Additional relationships from views
  @OneToMany(() => IventUsers, (iventUsers) => iventUsers.user)
  ivent_users?: IventUsers[];

  @OneToMany(() => IventUsers, (iventUsers) => iventUsers.inviter)
  ivent_user_invitations?: IventUsers[];

  @OneToMany(() => ActiveSessionIvents, (activeSessionIvents) => activeSessionIvents.user)
  active_session_ivents?: ActiveSessionIvents[];

  @OneToMany(() => CollabSummaryOfIvent, (collabSummaryOfIvent) => collabSummaryOfIvent.user)
  collab_summary_of_ivent?: CollabSummaryOfIvent[];

  @OneToMany(() => MemberSummaryOfSessionSquad, (memberSummaryOfSessionSquad) => memberSummaryOfSessionSquad.user)
  member_summary_of_session_squad?: MemberSummaryOfSessionSquad[];

  @OneToMany(() => SessionFriendSummaryOfIvent, (sessionFriendSummaryOfIvent) => sessionFriendSummaryOfIvent.user)
  session_friend_summary_of_ivent?: SessionFriendSummaryOfIvent[];

  @OneToMany(() => SquadFriendships, (squadFriendships) => squadFriendships.member)
  squad_friendships?: SquadFriendships[];

  @OneToMany(() => SquadFriendships, (squadFriendships) => squadFriendships.other_member)
  squad_friendships_as_other_member?: SquadFriendships[];

  @OneToMany(() => UserFriendships, (userFriendships) => userFriendships.user)
  user_friendships?: UserFriendships[];

  @OneToMany(() => UserFriendships, (userFriendships) => userFriendships.friend)
  user_friendships_as_friend?: UserFriendships[];

  @OneToOne(() => UserProfileStats, (userProfileStats) => userProfileStats.user)
  user_profile_stats?: UserProfileStats[];
}
