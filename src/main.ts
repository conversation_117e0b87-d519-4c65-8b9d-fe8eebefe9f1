import 'reflect-metadata';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import * as cookieParser from 'cookie-parser';
import { readFileSync } from 'fs';
import { join } from 'path';
import { AppModule } from './app.module';
import { AppLoggerInterceptor } from './interceptors/app-logger-interceptor';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const host = process.env.SERVER_HOST || 'localhost';
  const port = process.env.SERVER_TCP_PORT ? Number(process.env.SERVER_TCP_PORT) : 3000;

  let customCss = '';
  try {
    // Load CSS variables file
    const darkCssPath = join(process.cwd(), 'swagger-dark.css');
    const darkCss = readFileSync(darkCssPath, 'utf8');

    // Load CSS styling rules file
    const baseCssPath = join(process.cwd(), 'swagger-base.css');
    const baseCss = readFileSync(baseCssPath, 'utf8');

    // Combine both CSS files
    customCss = darkCss + '\n\n' + baseCss;
  } catch (error) {
    console.error('Failed to load custom CSS:', (error as Error).message);
  }

  // Configure body parser with increased limits for large payloads (like base64 images)
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  app.useGlobalInterceptors(new AppLoggerInterceptor());

  app.enableCors({
    origin: '*', // Allow all origins, be cautious in production
    methods: 'GET,POST,PUT,DELETE',
    allowedHeaders: 'Content-Type, Authorization',
  });

  app.use(cookieParser());
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: false, // Allow extra properties but strip them
      disableErrorMessages: false, // Show validation errors for better debugging
      transformOptions: {
        enableImplicitConversion: true, // Allow implicit type conversion
      },
    }),
  );

  if (process.env.USE_SWAGGER !== 'false') {
    const config = new DocumentBuilder()
      .setTitle('Ivent API Documentation')
      .setDescription(`You can download the API documentation in JSON format from http://${host}:${port}/api-json`)
      .setVersion('0.0.1')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
        'JWT-auth',
      )
      .addSecurityRequirements('JWT-auth')
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      deepScanRoutes: true,
    });

    const options = {
      customCss,
      customSiteTitle: 'Ivent API Documentation',
      explorer: true,
      swaggerOptions: {
        docExpansion: 'none',
        persistAuthorization: true,
      },
    };

    SwaggerModule.setup('api', app, document, options);
  }

  await app.listen(port, '0.0.0.0', () => {
    console.log(`Server is running at http://${host}:${port}`);
    console.log(`Healthcheck at http://${host}:${port}/health`);
    console.log(`Go to Swagger API -> http://${host}:${port}/api`);
    console.log(`To download the api-json.json, go to -> http://${host}:${port}/api-json`);
  });
}
bootstrap();
