import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModuleAsyncOptions } from '@nestjs/typeorm';

export const typeOrmConfigAsync: TypeOrmModuleAsyncOptions = {
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => {
    const isProd = configService.get<string>('NODE_ENV') === 'production';
    return {
      type: 'postgres',
      username: configService.get<string>('POSTGRES_USER')!,
      password: configService.get<string>('POSTGRES_PASSWORD')!,
      database: configService.get<string>('POSTGRES_DB')!,
      host: configService.get<string>('POSTGRES_HOST')!,
      port: configService.get<number>('POSTGRES_PORT')!,
      retryAttempts: 1,
      logging: !isProd,
      entities: [__dirname + '/../**/*.entity{.ts,.js}'],
      migrations: [__dirname + '/../migrations/*{.ts,.js}'],
      migrationsRun: !isProd,
    };
  },
  inject: [ConfigService],
};
