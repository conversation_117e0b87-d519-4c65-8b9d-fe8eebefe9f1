import * as dotenv from 'dotenv';
import 'reflect-metadata';
import { DataSource } from 'typeorm';
import * as entities from './entities';

// Load env
dotenv.config({ path: './old/.env' });

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.POSTGRES_HOST!,
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  username: process.env.POSTGRES_USER!,
  password: process.env.POSTGRES_PASSWORD!,
  database: process.env.POSTGRES_DB!,
  ssl: {
    rejectUnauthorized: false, // needed for Aiven
  },
  entities: Object.values(entities),
  migrations: [__dirname + '/migrations/*.{ts,js}'],
  synchronize: true, // never use `true` in production
});
