import * as dotenv from 'dotenv';
import 'reflect-metadata';
import { DataSource } from 'typeorm';
import * as entities from './entities';

// Load environment variables from multiple possible locations
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

// Determine if we're in production environment
const isProduction = process.env.NODE_ENV === 'production';
const isLocal = process.env.ENVIRONMENT === 'local';

// SSL configuration based on environment
const sslConfig =
  isProduction && !isLocal
    ? {
        rejectUnauthorized: false, // needed for cloud providers like Aiven
      }
    : false;

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.POSTGRES_HOST!,
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  username: process.env.POSTGRES_USER!,
  password: process.env.POSTGRES_PASSWORD!,
  database: process.env.POSTGRES_DB!,
  ssl: sslConfig,
  entities: Object.values(entities),
  migrations: [__dirname + '/migrations/*.{ts,js}'],
  synchronize: process.env.TYPEORM_SYNCHRONIZE === 'true' || (!isProduction && isLocal),
  logging: process.env.TYPEORM_LOGGING === 'true' || !isProduction,
  dropSchema: process.env.TYPEORM_DROP_SCHEMA === 'true',
  migrationsRun: !isProduction, // Auto-run migrations in development
});
