export enum IventCreatorTypeEnum {
  USER = 'user', // Creator is a user
  PAGE = 'page', // Creator is a page
  DISTRIBUTOR = 'distributor', // Creator is a distributor
}

export enum IventPrivacyEnum {
  PRIVATE = 'private', // Only visible to the creator and collaborators
  FRIENDS = 'friends', // Visible to the creator's friends
  EDU = 'edu', // Visible to users with verified educational status
  SELECTED_EDU = 'selected_edu', // Visible to users with selected universities
  PUBLIC = 'public', // Visible to everyone
}
