# 🐳 Docker Local Development Setup

Complete Docker-based local development environment for ivent-api NestJS application.

## 🚀 Quick Start

```bash
# 1. Setup environment
./scripts/setup-local-dev.sh

# 2. Test everything works
./scripts/test-local-deployment.sh

# 3. Start developing!
# API: http://localhost:3000
# Swagger: http://localhost:3000/api
```

## 📦 What's Included

### Core Services

- **NestJS API** (Port 3000) - Your application with hot reloading
- **PostgreSQL 15** (Port 5432) - Production-like database
- **LocalStack** (Port 4566) - Local AWS services (S3, SSM, etc.)
- **Redis** (Port 6379) - Caching layer

### Development Tools (Optional)

- **pgAdmin** (Port 5050) - Database management UI

### Features

- ✅ Hot reloading for code changes
- ✅ Database migrations with TypeORM
- ✅ Local S3 with bucket structure
- ✅ Production-like environment
- ✅ Comprehensive testing suite
- ✅ Development helper scripts

## 🛠️ Setup Commands

| Command                                   | Description                    |
| ----------------------------------------- | ------------------------------ |
| `./scripts/setup-local-dev.sh`            | Full setup                     |
| `./scripts/setup-local-dev.sh --clean`    | Clean setup (removes all data) |
| `./scripts/setup-local-dev.sh --tools`    | Include pgAdmin                |
| `./scripts/setup-local-dev.sh --no-build` | Skip Docker build              |

## 🧪 Testing Commands

| Command                                        | Description          |
| ---------------------------------------------- | -------------------- |
| `./scripts/test-local-deployment.sh`           | Full test suite      |
| `./scripts/test-local-deployment.sh --quick`   | Essential tests only |
| `./scripts/test-local-deployment.sh --verbose` | Detailed output      |

## 🔧 Helper Commands

```bash
# Development helpers
./scripts/dev-helpers.sh help        # Show all commands
./scripts/dev-helpers.sh logs        # View logs
./scripts/dev-helpers.sh restart     # Restart backend
./scripts/dev-helpers.sh db          # Connect to database
./scripts/dev-helpers.sh s3          # List S3 buckets
./scripts/dev-helpers.sh migrate     # Run migrations
./scripts/dev-helpers.sh status      # Service status
./scripts/dev-helpers.sh fix-localstack # Fix LocalStack issues

# Troubleshooting
./scripts/diagnose-env.sh            # Environment diagnostics
./scripts/fix-localstack.sh          # LocalStack troubleshooting
```

## 📋 Service URLs

| Service      | URL                          | Credentials                  |
| ------------ | ---------------------------- | ---------------------------- |
| API Server   | http://localhost:3000        | -                            |
| API Docs     | http://localhost:3000/api    | -                            |
| Health Check | http://localhost:3000/health | -                            |
| pgAdmin      | http://localhost:5050        | <EMAIL> / admin123 |
| LocalStack   | http://localhost:4566        | test / test                  |

## 🗄️ Database Connection

**Internal (Docker):**

- Host: `postgres`
- Port: `5432`

**External (Host machine):**

- Host: `localhost`
- Port: `5432`
- Database: `ivent_dev`
- Username: `ivent_user`
- Password: `dev_password_123`

## 🪣 S3 Configuration

**LocalStack S3:**

- Endpoint: http://localhost:4566
- Bucket: `ivent-media-dev`
- Access Key: `test`
- Secret Key: `test`
- Region: `eu-central-1`

**Bucket Structure:**

```
ivent-media-dev/
├── vibes/
├── thumbnails/
│   └── vibes/
├── ivents/
└── memories/
```

## 🔄 Development Workflow

1. **Start services:**

   ```bash
   docker-compose up -d
   ```

2. **Make code changes** - Hot reloading is enabled

3. **View logs:**

   ```bash
   docker-compose logs -f backend
   ```

4. **Run migrations:**

   ```bash
   docker-compose exec backend npm run migration:run
   ```

5. **Test changes:**
   ```bash
   ./scripts/test-local-deployment.sh
   ```

## 🐛 Troubleshooting

### Common Issues

**Port conflicts:**

```bash
# Check what's using port 3000
lsof -i :3000

# Change port in .env
SERVER_TCP_PORT=3001
```

**Services not starting:**

```bash
# Check service status
docker-compose ps

# View service logs
docker-compose logs [service-name]

# Restart service
docker-compose restart [service-name]
```

**Database connection issues:**

```bash
# Test database connection
docker-compose exec postgres pg_isready -U ivent_user -d ivent_dev

# Connect to database
docker-compose exec postgres psql -U ivent_user -d ivent_dev
```

**LocalStack not responding:**

```bash
# Check LocalStack health
curl http://localhost:4566/_localstack/health

# Restart LocalStack
docker-compose restart localstack

# Fix LocalStack issues automatically
./scripts/fix-localstack.sh

# Clean LocalStack data and restart
./scripts/fix-localstack.sh --clean
```

**LocalStack taking too long to start:**

```bash
# LocalStack can take 1-2 minutes to start, especially on first run
# Check the logs to see what's happening
docker-compose logs -f localstack

# Use the troubleshooting script
./scripts/fix-localstack.sh
```

### Environment Diagnostics

```bash
# Run comprehensive diagnostics
./scripts/diagnose-env.sh

# This checks:
# - Docker installation and status
# - Port availability
# - Service connectivity
# - Environment files
# - Common issues
```

### Clean Reset

```bash
# Complete reset
./scripts/setup-local-dev.sh --clean

# Or manual cleanup
docker-compose down -v --remove-orphans
docker system prune -f
```

## 📁 File Structure

```
ivent_api/
├── docker-compose.yml              # Enhanced development services
├── Dockerfile.dev                  # Development-optimized Dockerfile
├── .env.local                      # Local environment template
├── docker/
│   ├── localstack/init/            # LocalStack initialization
│   └── postgres/init/              # PostgreSQL initialization
├── scripts/
│   ├── setup-local-dev.sh          # Main setup script
│   ├── test-local-deployment.sh    # Testing script
│   └── dev-helpers.sh              # Development utilities
└── docs/
    ├── LOCAL_DEVELOPMENT_GUIDE.md  # Detailed guide
    └── DOCKER_SETUP.md             # This file
```

## 🎯 Next Steps

1. **Read the detailed guide:** [LOCAL_DEVELOPMENT_GUIDE.md](docs/LOCAL_DEVELOPMENT_GUIDE.md)
2. **Setup your environment:** `./scripts/setup-local-dev.sh`
3. **Run tests:** `./scripts/test-local-deployment.sh`
4. **Start developing!** 🚀

## 💡 Tips

- Use `./scripts/dev-helpers.sh` for common tasks
- Enable tools profile for pgAdmin: `--tools` flag
- Check service health with `docker-compose ps`
- View real-time logs with `docker-compose logs -f`
- Hot reloading works for TypeScript files in `src/`

---

**Need help?** Check the [troubleshooting section](#-troubleshooting) or the [detailed guide](docs/LOCAL_DEVELOPMENT_GUIDE.md).
