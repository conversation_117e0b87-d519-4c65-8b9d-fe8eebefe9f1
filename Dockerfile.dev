# Multi-stage Dockerfile optimized for local development

# Stage 1: Base development image
FROM node:20-alpine AS base

# Install system dependencies for development
RUN apk add --no-cache \
    curl \
    bash \
    git \
    openssh-client \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

# Set working directory
WORKDIR /app

# Change ownership to non-root user
RUN chown -R nestjs:nodejs /app

# Switch to non-root user
USER nestjs

# Stage 2: Dependencies installation
FROM base AS dependencies

# Copy package files
COPY --chown=nestjs:nodejs package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci --include=dev && npm cache clean --force

# Stage 3: Development stage
FROM dependencies AS development

# Copy source code
COPY --chown=nestjs:nodejs . .

# Create necessary directories
RUN mkdir -p /app/certs /app/logs

# Expose ports
EXPOSE 3000 9229

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Development command with hot reloading and debugging
CMD ["npm", "run", "start:debug"]

# Stage 4: Production build (for testing production builds locally)
FROM dependencies AS build

# Copy source code
COPY --chown=nestjs:nodejs . .

# Build the application
RUN npm run build

# Stage 5: Production stage (lightweight)
FROM base AS production

# Copy package files
COPY --chown=nestjs:nodejs package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application from build stage
COPY --from=build --chown=nestjs:nodejs /app/dist ./dist

# Copy necessary files
COPY --chown=nestjs:nodejs swagger-*.css ./

# Create directory for SSL certificates
RUN mkdir -p /app/certs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start the application
CMD ["node", "dist/main.js"]
