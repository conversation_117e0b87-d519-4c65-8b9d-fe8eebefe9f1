# Local Development Environment Guide

This guide provides comprehensive instructions for setting up and using the Docker-based local development environment for the ivent-api NestJS application.

## 🚀 Quick Start

### Prerequisites

- Docker Desktop (v20.10+)
- Docker Compose (v2.0+)
- Node.js (v20+) - for running scripts outside containers
- AWS CLI (for LocalStack S3 testing)

### One-Command Setup

```bash
# Clone and setup everything
git clone <your-repo-url>
cd ivent_api
./scripts/setup-local-dev.sh
```

This will:

- Build all Docker images
- Start PostgreSQL, LocalStack (S3), Redis, and the API
- Run database migrations
- Setup S3 buckets with proper structure
- Verify all services are healthy

## 📋 Services Overview

| Service        | Port | Description                    | Health Check                               |
| -------------- | ---- | ------------------------------ | ------------------------------------------ |
| **API Server** | 3000 | NestJS application             | http://localhost:3000/health               |
| **PostgreSQL** | 5432 | Database                       | `docker-compose exec postgres pg_isready`  |
| **LocalStack** | 4566 | AWS services (S3, SSM, etc.)   | http://localhost:4566/\_localstack/health  |
| **Redis**      | 6379 | Caching (optional)             | `docker-compose exec redis redis-cli ping` |
| **pgAdmin**    | 5050 | Database management (optional) | http://localhost:5050                      |

## 🛠️ Setup Options

### Standard Setup

```bash
./scripts/setup-local-dev.sh
```

### Clean Setup (removes all data)

```bash
./scripts/setup-local-dev.sh --clean
```

### Include Development Tools

```bash
./scripts/setup-local-dev.sh --tools
```

### Skip Docker Build

```bash
./scripts/setup-local-dev.sh --no-build
```

## 🔧 Development Workflow

### Starting Development

1. **Start all services:**

   ```bash
   docker-compose up -d
   ```

2. **View logs:**

   ```bash
   # All services
   docker-compose logs -f

   # Specific service
   docker-compose logs -f backend
   ```

3. **Access the application:**
   - API: http://localhost:3000
   - Swagger UI: http://localhost:3000/api
   - Health Check: http://localhost:3000/health

### Hot Reloading

The development setup includes hot reloading:

- Source code is mounted as a volume
- Changes to `src/` directory trigger automatic restarts
- No need to rebuild Docker images for code changes

### Database Operations

```bash
# Run migrations
docker-compose exec backend npm run migration:run

# Generate new migration
docker-compose exec backend npm run migration:generate -- MigrationName

# Access database directly
docker-compose exec postgres psql -U ivent_user -d ivent_dev

# Reset database (careful!)
docker-compose exec backend npm run typeorm:reset
```

### S3 Operations (LocalStack)

```bash
# List buckets
aws --endpoint-url=http://localhost:4566 s3 ls

# List bucket contents
aws --endpoint-url=http://localhost:4566 s3 ls s3://ivent-media-dev/

# Upload test file
aws --endpoint-url=http://localhost:4566 s3 cp test.jpg s3://ivent-media-dev/test/
```

## 🧪 Testing

### Run All Tests

```bash
./scripts/test-local-deployment.sh
```

### Quick Tests Only

```bash
./scripts/test-local-deployment.sh --quick
```

### Verbose Output

```bash
./scripts/test-local-deployment.sh --verbose
```

## 🔨 Helper Commands

Use the development helper script for common tasks:

```bash
# Show all available commands
./scripts/dev-helpers.sh help

# View service logs
./scripts/dev-helpers.sh logs backend

# Restart a service
./scripts/dev-helpers.sh restart backend

# Open shell in container
./scripts/dev-helpers.sh shell backend

# Connect to database
./scripts/dev-helpers.sh db

# Connect to Redis
./scripts/dev-helpers.sh redis

# List S3 buckets
./scripts/dev-helpers.sh s3

# Run migrations
./scripts/dev-helpers.sh migrate

# Show service status
./scripts/dev-helpers.sh status

# Clean up everything
./scripts/dev-helpers.sh clean
```

## 📁 Project Structure

```
ivent_api/
├── docker/
│   ├── localstack/
│   │   └── init/
│   │       └── setup-s3.sh          # S3 bucket initialization
│   └── postgres/
│       └── init/                    # PostgreSQL initialization scripts
├── scripts/
│   ├── setup-local-dev.sh          # Main setup script
│   ├── test-local-deployment.sh    # Local testing script
│   ├── dev-helpers.sh              # Development utilities
│   └── setup-local-s3.sh           # S3 setup (existing)
├── docker-compose.yml              # Enhanced development services
├── Dockerfile.dev                  # Development-optimized Dockerfile
├── .env.local                      # Local environment template
└── docs/
    └── LOCAL_DEVELOPMENT_GUIDE.md  # This guide
```

## ⚙️ Configuration

### Environment Variables

The `.env.local` file contains all necessary environment variables for local development. Key configurations:

```bash
# Database
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=ivent_user
POSTGRES_PASSWORD=dev_password_123
POSTGRES_DB=ivent_dev

# AWS/LocalStack
AWS_ENDPOINT_URL=http://localstack:4566
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_S3_BUCKET_NAME=ivent-media-dev

# Development
NODE_ENV=development
TYPEORM_LOGGING=true
TYPEORM_SYNCHRONIZE=false
```

### Docker Compose Profiles

- **Default**: Core services (postgres, localstack, redis, backend)
- **Tools**: Includes pgAdmin for database management

```bash
# Start with tools
docker-compose --profile tools up -d
```

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts:**

   ```bash
   # Check what's using the port
   lsof -i :3000

   # Change port in .env
   SERVER_TCP_PORT=3001
   ```

2. **Database connection issues:**

   ```bash
   # Check PostgreSQL status
   docker-compose exec postgres pg_isready -U ivent_user -d ivent_dev

   # Restart PostgreSQL
   docker-compose restart postgres
   ```

3. **LocalStack not responding:**

   ```bash
   # Check LocalStack health
   curl http://localhost:4566/_localstack/health

   # Restart LocalStack
   docker-compose restart localstack
   ```

4. **Hot reloading not working:**

   ```bash
   # Check volume mounts
   docker-compose exec backend ls -la /app/src

   # Restart backend
   docker-compose restart backend
   ```

### Clean Reset

If you encounter persistent issues:

```bash
# Complete clean reset
./scripts/setup-local-dev.sh --clean

# Or manually
docker-compose down -v --remove-orphans
docker system prune -f
./scripts/setup-local-dev.sh
```

### Logs and Debugging

```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f backend

# Check container status
docker-compose ps

# Inspect container
docker-compose exec backend env
```

## 🔗 Integration with Production

This local environment mirrors your production setup:

- **PostgreSQL 15**: Same version as RDS
- **Node.js 20**: Same runtime as ECS
- **Environment variables**: Similar structure to production
- **S3 compatibility**: LocalStack provides S3-compatible API
- **SSL/TLS**: Disabled for local development, enabled in production

### Migration Testing

Test your migrations locally before deploying:

```bash
# Test migration
docker-compose exec backend npm run migration:generate -- TestMigration

# Review generated migration
docker-compose exec backend cat src/migrations/[timestamp]-TestMigration.ts

# Run migration
docker-compose exec backend npm run migration:run

# Revert if needed
docker-compose exec backend npm run migration:revert
```

## 📚 Additional Resources

- [NestJS Documentation](https://docs.nestjs.com/)
- [TypeORM Documentation](https://typeorm.io/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [LocalStack Documentation](https://docs.localstack.cloud/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## 🎯 Quick Reference

### Essential Commands

```bash
# Setup
./scripts/setup-local-dev.sh

# Test
./scripts/test-local-deployment.sh

# Helpers
./scripts/dev-helpers.sh help

# Docker Compose
docker-compose up -d          # Start all services
docker-compose logs -f        # View logs
docker-compose restart backend # Restart service
docker-compose down           # Stop all services
```

### Key URLs

- API: http://localhost:3000
- Swagger: http://localhost:3000/api
- Health: http://localhost:3000/health
- pgAdmin: http://localhost:5050 (with --tools)

### Database Access

```bash
# Via Docker
docker-compose exec postgres psql -U ivent_user -d ivent_dev

# External connection
Host: localhost:5432
Database: ivent_dev
Username: ivent_user
Password: dev_password_123
```

## 🤝 Contributing

When making changes to the local development environment:

1. Test your changes with `./scripts/test-local-deployment.sh`
2. Update this documentation if needed
3. Ensure backward compatibility
4. Test both clean and incremental setups
